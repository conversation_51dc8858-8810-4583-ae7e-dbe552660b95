<?php
return [
    'Chart Of Accounts'=>'खातों का संचित्र',
    'New Account'=>' नया खाता',
    'Add As A Parent Account'=>' एक जनक खाते के रूप में जोड़ें',
    'Parent account selection will add your account as a sub account'=>'अभिभावक खाते का चयन आपके खाते को उप खाते के रूप में जोड़ देगा',
    'Select your Expense Account'=>'अपना व्यय खाता चुनें',
    'Select your Income Account'=>'अपना आय खाता चुनें',
    'Select your Payment method'=>'अपनी भुगतान विधि चुनें',
    'Account code need to be unique. Leave blank for auto generate an unique account code'=>'खाता कोड अद्वितीय होना चाहिए। ऑटो के लिए खाली छोड़ना एक अद्वितीय खाता कोड उत्पन्न करता है',
    'Selecting a default Account, will remove previously default account for selected item'=>'एक डिफ़ॉल्ट खाता का चयन, चयनित आइटम के लिए पहले डिफ़ॉल्ट खाते को हटा देगा',
    'Select Default Account For'=>'के लिए डिफ़ॉल्ट खाता चुनें',
    "You can't delete an account which has child element"=>'आप उस खाते को नहीं हटा सकते जिसमें बाल तत्व है',
    'The requested chart of account is not found'=>'खाते का अनुरोधित चार्ट नहीं मिला है',
    'The requested chart of account deleted successful'=>'खाते का अनुरोधित चार्ट सफल हटा दिया गया',
    'The requested chart of account created successful'=>'खाते का अनुरोधित चार्ट सफल बना',
    'Income Account'=>'आय खाता',
    'Expense Account'=>'खर्च का हिसाब',
    'Select Account'=>'खाता चुनें',
    'Payment Method'=>'भुगतान का तरीका',
    'Edit Account' => 'खाता संपादित करें',
    'The requested chart of account updated successful' => 'खाते का अनुरोधित चार्ट सफल हुआ',
];
