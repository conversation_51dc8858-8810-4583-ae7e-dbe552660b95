@keyframes dtb-spinner {
	100% {
		transform: rotate(360deg);
	}
}
@-o-keyframes dtb-spinner {
	100% {
		-o-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}
@-ms-keyframes dtb-spinner {
	100% {
		-ms-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}
@-webkit-keyframes dtb-spinner {
	100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}
@-moz-keyframes dtb-spinner {
	100% {
		-moz-transform: rotate(360deg);
		transform: rotate(360deg);
	}
}
div.dt-button-info {
	position: fixed;
	top: 50%;
	left: 50%;
	width: 400px;
	margin-top: -100px;
	margin-left: -200px;
	background-color: white;
	border: 2px solid #111;
	box-shadow: 3px 3px 8px rgba(0, 0, 0, 0.3);
	border-radius: 3px;
	text-align: center;
	z-index: 21;
}
div.dt-button-info h2 {
	padding: 0.5em;
	margin: 0;
	font-weight: normal;
	border-bottom: 1px solid #ddd;
	background-color: #f3f3f3;
}
div.dt-button-info > div {
	padding: 1em;
}

button.dt-button,
div.dt-button,
a.dt-button {
	border: 1px solid transparent;
	background: transparent;
	line-height: 27px;
	border-left: 1px solid #415094;
	padding: 0 10px;
	position: relative;
	display: inline-block;
	box-sizing: border-box;
	margin: 0;
	cursor: pointer;
	color: black;
	white-space: nowrap;
	overflow: hidden;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	text-decoration: none;
	outline: none;
}
button.dt-button:first-of-type {
	border-left: 0px;
}
button.dt-button.disabled,
div.dt-button.disabled,
a.dt-button.disabled {

	color: #ffffff;
	border: 1px solid #d0d0d0;
	cursor: pointer;
	background-color: #f9f9f9;
	background-image: -webkit-linear-gradient(top, #fff 0%, #f9f9f9 100%);
	background-image: -moz-linear-gradient(top, #fff 0%, #f9f9f9 100%);
	background-image: -ms-linear-gradient(top, #fff 0%, #f9f9f9 100%);
	background-image: -o-linear-gradient(top, #fff 0%, #f9f9f9 100%);
	background-image: linear-gradient(to bottom, #fff 0%, #f9f9f9 100%);
	filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, StartColorStr='#ffffff', EndColorStr='#f9f9f9');
}
button.dt-button:active:not(.disabled),
button.dt-button.active:not(.disabled),
div.dt-button:active:not(.disabled),
div.dt-button.active:not(.disabled),
a.dt-button:active:not(.disabled),
a.dt-button.active:not(.disabled) {
	border: 0;
	background-color: #7c32ff;
	color: #ffffff;
	background: -webkit-linear-gradient(90deg, #7c32ff 0%, #c738d8 100%);
	background: -moz-linear-gradient(90deg, #7c32ff 0%, #c738d8 100%);
	background: -o-linear-gradient(90deg, #7c32ff 0%, #c738d8 100%);
	background: linear-gradient(90deg, #7c32ff 0%, #c738d8 100%);
	filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, StartColorStr='#7c32ff', EndColorStr='#c738d8');
}
div.dt-button.active:not(.disabled) span {
	color: #ffffff;
}
button.dt-button:active:not(.disabled):hover:not(.disabled),
button.dt-button.active:not(.disabled):hover:not(.disabled),
div.dt-button:active:not(.disabled):hover:not(.disabled),
div.dt-button.active:not(.disabled):hover:not(.disabled),
a.dt-button:active:not(.disabled):hover:not(.disabled),
a.dt-button.active:not(.disabled):hover:not(.disabled) {
	cursor: pointer;
	border: 0;
	background-color: #7c32ff;
	color: #ffffff;
	background: -webkit-linear-gradient(90deg, #7c32ff 0%, #c738d8 100%);
	background: -moz-linear-gradient(90deg, #7c32ff 0%, #c738d8 100%);
	background: -o-linear-gradient(90deg, #7c32ff 0%, #c738d8 100%);
	background: linear-gradient(90deg, #7c32ff 0%, #c738d8 100%);
	filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, StartColorStr='#7c32ff', EndColorStr='#c738d8');
}
button.dt-button:hover,
div.dt-button:hover,
a.dt-button:hover {
	text-decoration: none;
}
button.dt-button:hover:not(.disabled),
div.dt-button:hover:not(.disabled),
a.dt-button:hover:not(.disabled) {
	border: 1px solid transparent;
	background-color: #7c32ff;
	background: -webkit-linear-gradient(90deg, #7c32ff 0%, #c738d8 100%);
	background: -moz-linear-gradient(90deg, #7c32ff 0%, #c738d8 100%);
	background: -o-linear-gradient(90deg, #7c32ff 0%, #c738d8 100%);
	background: linear-gradient(90deg, #7c32ff 0%, #c738d8 100%);
	filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, StartColorStr='#7c32ff', EndColorStr='#c738d8');
}
button.buttons-columnVisibility {
    border: 0;
    background: transparent;
    box-shadow: none;
}
button.buttons-columnVisibility:hover:not(.disabled) {
    border: 0;
}
button.buttons-columnVisibility span {
    border: 0;
}
button.dt-button span {
	color: #415094;
	font-size: 13px;
	font-weight: 500;
	-webkit-transition: all 0.4s ease 0s;
	-moz-transition: all 0.4s ease 0s;
	-o-transition: all 0.4s ease 0s;
	transition: all 0.4s ease 0s;
}
button.dt-button:hover span {
	color: #ffffff;
}
/* button.dt-button:focus:not(.disabled),
div.dt-button:focus:not(.disabled),
a.dt-button:focus:not(.disabled) {
	border: 1px solid #426c9e;
	text-shadow: 0 1px 0 #c4def1;
	outline: none;
	background-color: #79ace9;
	background-image: -webkit-linear-gradient(top, #bddef4 0%, #79ace9 100%);
	background-image: -moz-linear-gradient(top, #bddef4 0%, #79ace9 100%);
	background-image: -ms-linear-gradient(top, #bddef4 0%, #79ace9 100%);
	background-image: -o-linear-gradient(top, #bddef4 0%, #79ace9 100%);
	background-image: linear-gradient(to bottom, #bddef4 0%, #79ace9 100%);
	filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0, StartColorStr='#bddef4', EndColorStr='#79ace9');
} */
.dt-button embed {
	outline: none;
}
div.dt-buttons {
	position: relative;
	bottom: 22px;
	float: right;
	background: transparent;
	border: 1px solid #7c32ff;
	border-radius: 31px;
	height: 30px;
	line-height: 27px;
	-webkit-transition: all 0.4s ease 0s;
	-moz-transition: all 0.4s ease 0s;
	-o-transition: all 0.4s ease 0s;
	transition: all 0.4s ease 0s;
	padding: 0 20px;
}

div.dt-buttons.buttons-right {
	float: right;
}
div.dt-button-collection {
	position: absolute;
	top: 0;
	left: 108px!important;
	width: 150px;
	margin-top: 3px;
	padding: 8px 8px 4px 8px;
	border: 0;
	background-color: white;
	overflow: hidden;
	z-index: 2002;
	border-radius: 5px;
	box-shadow: 3px 3px 5px rgba(0, 0, 0, 0.3);
	-webkit-column-gap: 8px;
	-moz-column-gap: 8px;
	-ms-column-gap: 8px;
	-o-column-gap: 8px;
	column-gap: 8px;
	box-shadow: 0px 10px 20px rgba(108, 39, 255, 0.3);
}
div.dt-button-collection button.dt-button,
div.dt-button-collection div.dt-button,
div.dt-button-collection a.dt-button {
	position: relative;
	left: 0;
	right: 0;
	width: 100%;
	display: block;
	float: none;
	margin-bottom: 4px;
	margin-right: 0;
	border: 0;
}
div.dt-button-collection button.dt-button:active:not(.disabled),
div.dt-button-collection button.dt-button.active:not(.disabled),
div.dt-button-collection div.dt-button:active:not(.disabled),
div.dt-button-collection div.dt-button.active:not(.disabled),
div.dt-button-collection a.dt-button:active:not(.disabled),
div.dt-button-collection a.dt-button.active:not(.disabled) {
    color: #ffffff;
}
div.dt-button-collection button.dt-button.active:not(.disabled) span {
	color: #ffffff;
}
div.dt-button-collection.fixed {
	position: fixed;
	top: 50%;
	left: 50%;
	margin-left: -75px;
	border-radius: 0;
}
div.dt-button-collection.fixed.two-column {
	margin-left: -150px;
}
div.dt-button-collection.fixed.three-column {
	margin-left: -225px;
}
div.dt-button-collection.fixed.four-column {
	margin-left: -300px;
}
div.dt-button-collection > * {
	-webkit-column-break-inside: avoid;
	break-inside: avoid;
}
div.dt-button-collection.two-column {
	width: 300px;
	padding-bottom: 1px;
	-webkit-column-count: 2;
	-moz-column-count: 2;
	-ms-column-count: 2;
	-o-column-count: 2;
	column-count: 2;
}
div.dt-button-collection.three-column {
	width: 450px;
	padding-bottom: 1px;
	-webkit-column-count: 3;
	-moz-column-count: 3;
	-ms-column-count: 3;
	-o-column-count: 3;
	column-count: 3;
}
div.dt-button-collection.four-column {
	width: 600px;
	padding-bottom: 1px;
	-webkit-column-count: 4;
	-moz-column-count: 4;
	-ms-column-count: 4;
	-o-column-count: 4;
	column-count: 4;
}
div.dt-button-collection .dt-button {
	border-radius: 0;
}
div.dt-button-background {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	/* background: rgba(0, 0, 0, 0.7);
	background: -ms-radial-gradient(center, ellipse farthest-corner, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.7) 100%);
	background: -moz-radial-gradient(center, ellipse farthest-corner, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.7) 100%);
	background: -o-radial-gradient(center, ellipse farthest-corner, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.7) 100%);
	background: -webkit-gradient(
		radial,
		center center,
		0,
		center center,
		497,
		color-stop(0, rgba(0, 0, 0, 0.3)),
		color-stop(1, rgba(0, 0, 0, 0.7))
	);
	background: -webkit-radial-gradient(
		center,
		ellipse farthest-corner,
		rgba(0, 0, 0, 0.3) 0%,
		rgba(0, 0, 0, 0.7) 100%
	);
	background: radial-gradient(ellipse farthest-corner at center, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.7) 100%);
	z-index: 2001; */
}
@media screen and (max-width: 640px) {
	div.dt-buttons {
		float: none !important;
		text-align: right;
	}
}
button.dt-button.processing,
div.dt-button.processing,
a.dt-button.processing {
	color: rgba(0, 0, 0, 0.2);
}
button.dt-button.processing:after,
div.dt-button.processing:after,
a.dt-button.processing:after {
	position: absolute;
	top: 50%;
	left: 50%;
	width: 16px;
	height: 16px;
	margin: -8px 0 0 -8px;
	box-sizing: border-box;
	display: block;
	content: ' ';
	border: 2px solid #282828;
	border-radius: 50%;
	border-left-color: transparent;
	border-right-color: transparent;
	animation: dtb-spinner 1500ms infinite linear;
	-o-animation: dtb-spinner 1500ms infinite linear;
	-ms-animation: dtb-spinner 1500ms infinite linear;
	-webkit-animation: dtb-spinner 1500ms infinite linear;
	-moz-animation: dtb-spinner 1500ms infinite linear;
}
