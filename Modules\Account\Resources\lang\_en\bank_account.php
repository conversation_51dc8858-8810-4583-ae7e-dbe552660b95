<?php
return [
    'Bank Accounts' => 'Bank Accounts',
    'New Account' => 'New Account',
    'Select Default Account For' => 'Select Default Account For',
    'The requested bank account deleted successful' => 'The requested bank account deleted successful',
    'The requested bank account created successful' => 'The requested bank account created successful',
    'The requested bank account updated successful' => 'The requested bank account updated successful',
    'Bank Name' => 'Bank Name',
    'title' => 'Title',
    'Branch Name' => 'Branch Name',
    'Account Name' => 'Account Name',
    'Account Number' => 'Account Number',
    "You can\'t delete an account which has child element" => "You can\'t delete an account which has child element",
    'The requested bank account is not found' => 'The requested bank account is not found',
];
