<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateHeaderSliderPanelsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('header_slider_panels', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('heading')->nullable();
            $table->string('sub_heading')->nullable();
            $table->text('url')->nullable();
            $table->string('data_type')->nullable();
            $table->unsignedBigInteger('data_id')->nullable();
            $table->string('slider_image')->nullable();
            $table->boolean('status')->default(1);
            $table->boolean('is_newtab')->default(1);
            $table->unsignedInteger('position')->default(598776);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('header_slider_panels');
    }
}
