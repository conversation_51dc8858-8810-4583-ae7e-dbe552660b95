<!DOCTYPE html>
<html lang="">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <link rel="icon" href="img/favicon.png" type="image/png" />
    <title>Amazcart</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="vendors/css/jquery-ui.css" />
    <link rel="stylesheet" href="vendors/css/bootstrap.css" />
    <link rel="stylesheet" href="vendors/css/jquery.data-tables.css">
    <link rel="stylesheet" href="vendors/css/buttons.dataTables.min.css">
    <link rel="stylesheet" href="vendors/css/rowReorder.dataTables.min.css">
    <link rel="stylesheet" href="vendors/css/responsive.dataTables.min.css">
    <link rel="stylesheet" href="vendors/css/bootstrap-datepicker.min.css" />
    <link rel="stylesheet" href="vendors/css/bootstrap-datetimepicker.min.css" />
    <link rel="stylesheet" href="vendors/css/themify-icons.css" />
    <link rel="stylesheet" href="vendors/css/flaticon.css" />
    <!-- <link rel="stylesheet" href="vendors/css/font-awesome.min.css" /> -->
    <link rel="stylesheet" href="vendors/css/magnific-popup.css" />
    <link rel="stylesheet" href="vendors/css/nice-select.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta2/css/all.min.css" />
    <!-- main css -->
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="css/documentation.css" />
</head>


<body class="version1" data-spy="scroll" data-target="#sidebar" data-offset="50">
    <div class="main-wrapper">
        <!-- Sidebar  -->
        <nav id="sidebar" class="active">
            <div class="sidebar-header">
                <a href="index.html">
                    <!-- <img src="img/logo.png" alt=""> -->
                </a>
            </div>

            <ul class="list-unstyled components">
                <li>
                    <a href="#initiate" class="active">
                        <span class="fa fa-angle-double-right"></span>
                        initiate
                    </a>
                </li>
                <li>
                    <a href="#system-requirements">
                        <span class="fa fa-align-center"></span>
                        System Requirements
                    </a>
                </li>

                <li>
                    <a href="#subMenuGettingStarted" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa-solid fa-play"></span>
                        Getting Started
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuGettingStarted">
                        <li>
                            <a href="#Installation" class="spy_scroll">Amazcart Installation</a>
                        </li>
                        <li>
                            <a href="#GetSupport" class="spy_scroll">Get Support</a>
                        </li>
                    </ul>
                </li>


                <!-- <li>
                    <a href="#Dashboard">
                        <span class="flaticon-dashboard"></span>
                        Dashboard
                    </a>
                </li> -->
                <li>
                    <a href="#subMenuDashboardSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <!-- <span class="fa fa-user"></span> -->
                        <span class="fa-solid fa-chart-line"></span>
                        Dashboard
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuDashboardSetting">
                        <li>
                            <a href="#Dashboard" class="spy_scroll">Dashboard </a>
                        </li>
                    </ul>
                </li>

                <li>
                    <a href="#subMenuAdminSection" data-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <span class="fa fa-asterisk"></span>
                        Frontend CMS
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuAdminSection">
                        <li>
                            <a href="#Homepage" class="spy_scroll">Homepage</a>
                        </li>
                        <li>
                            <a href="#Features" class="spy_scroll">Features</a>
                        </li>
                        <li>
                            <a href="#Merchent" class="spy_scroll">Merchent Content</a>
                        </li>
                        <li>
                            <a href="#ReturnExchange" class="spy_scroll">Return Exchange</a>
                        </li>
                        <li>
                            <a href="#ContanctContent" class="spy_scroll">Contanct Content</a>
                        </li>
                        <li>
                            <a href="#DynamicPages" class="spy_scroll">Dynamic Pages</a>
                        </li>
                        <li>
                            <a href="#FooterSetting" class="spy_scroll">Footer Setting</a>
                        </li>
                        <li>
                            <a href="#SubscribeContent" class="spy_scroll">Subscribe Content</a>
                        </li>
                        <li>
                            <a href="#AboutUs" class="spy_scroll">About Us</a>
                        </li>
                        <li>
                            <a href="#Title" class="spy_scroll">Related Sale Setting</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#subMenuApperenceSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa fa-sun"></span>
                        Apperence
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuApperenceSetting">
                        <li>
                            <a href="#Themes" class="spy_scroll">Themes</a>
                        </li>
                        <li>
                            <a href="#ColorScheme" class="spy_scroll">Color Scheme</a>
                        </li>
                        <li>
                            <a href="#Menus" class="spy_scroll">Menus</a>
                        </li>
                        <li>
                            <a href="#Header" class="spy_scroll">Header</a>
                        </li>
                        <li>
                            <a href="#DashboardSetup" class="spy_scroll">Dashboard Setup</a>
                        </li>
                        <li>
                            <a href="#DashboardColor" class="spy_scroll">Dashboard Color</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#subMenuBlogSetting" data-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <span class="fa fa-newspaper"></span>
                        Blog
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuBlogSetting">
                        <li>
                            <a href="#Blog" class="spy_scroll">Blog</a>
                        </li>
                        <li>
                            <a href="#BlogCategory" class="spy_scroll">Blog Category</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#subMenuCustomerSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa fa-users"></span>
                        Customer
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuCustomerSetting">
                        <li>
                            <a href="#Customer" class="spy_scroll">All Customer</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#subMenuSellerSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa fa-user-plus"></span>
                        Manage Seller
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuSellerSetting">
                        <li>
                            <a href="#SellerList" class="spy_scroll">Active Seller List</a>
                        </li>
                        <li>
                            <a href="#InactiveSeller" class="spy_scroll">Inactive/Request Seller List</a>
                        </li>
                        <li>
                            <a href="#ComissionSetup" class="spy_scroll">Comission Setup</a>
                        </li>

                        <li>
                            <a href="#PricingPlan" class="spy_scroll">Pricing Plan</a>
                        </li>
                        <li>
                            <a href="#SubscriptionPayment" class="spy_scroll">Subscription Payment</a>
                        </li>
                        <li>
                            <a href="#AutoApproveSellerConfiguration" class="spy_scroll">Configuration</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#subMenuWalletSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa fa-book"></span>
                        My Wallet
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuWalletSetting">
                        <li>
                            <a href="#MyWallet" class="spy_scroll">Transactions</a>
                        </li>
                        <li>
                            <a href="#Withdraw" class="spy_scroll">Withdraw</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#subMenuWalletManageSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <!-- <span class="fa fa-box-open"></span> -->
                        <span class="fa-solid fa-wallet"></span>
                        Wallet Manage
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuWalletManageSetting">
                        <li>
                            <a href="#OnlineRecharge" class="spy_scroll">Online Recharge</a>
                        </li>
                        <li>
                            <a href="#BankRecharge" class="spy_scroll">Bank Recharge</a>
                        </li>
                        <!-- <li>
                            <a href="#WalletRechargeApproval" class="spy_scroll">Wallet Recharge Approval</a>
                        </li> -->
                        <li>
                            <a href="#WithdrawRequest" class="spy_scroll">Withdraw Request</a>
                        </li>
                        <li>
                            <a href="#OfflineRecharge" class="spy_scroll">Offline Recharge</a>
                        </li>
                        <li>
                            <a href="#WalletConfiguration" class="spy_scroll">Configuration</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#subMenuContactSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <!-- <span class="fa fa-baseball-ball"></span> -->
                        <span class="fa-solid fa-address-card"></span>
                        Contact Request
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuContactSetting">
                        <li>
                            <a href="#ContactMail" class="spy_scroll">Contact Mail</a>
                        </li>
                    </ul>
                </li>



                <li>
                    <a href="#subMenuMarketingSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa fa-user-circle"></span>
                        Marketing
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuMarketingSetting">
                        <li>
                            <a href="#FlashDeals" class="spy_scroll">Flash Deals</a>
                        </li>
                        <li>
                            <a href="#Coupons" class="spy_scroll">Coupons</a>
                        </li>
                        <li>
                            <a href="#NewUserZone" class="spy_scroll">New User Zone</a>
                        </li>
                        <li>
                            <a href="#NewsLetter" class="spy_scroll">News Letter</a>
                        </li>
                        <li>
                            <a href="#BulkSms" class="spy_scroll">Bulk Sms</a>
                        </li>
                        <li>
                            <a href="#Subscription" class="spy_scroll">Subscription</a>
                        </li>
                        <li>
                            <a href="#ReferralCodeSetup" class="spy_scroll">Referral Code Setup</a>
                        </li>
                    </ul>
                </li>

                <li>
                    <a href="#subMenuGiftCardSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa fa-gift"></span>
                        Gift Card
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuGiftCardSetting">
                        <li>
                            <a href="#GiftCardList" class="spy_scroll">Gift Card List</a>
                        </li>
                    </ul>
                </li>

                <li>
                    <a href="#subMenuProductsSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa fa-clipboard-list"></span>
                        Products
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuProductsSetting">
                        <li>
                            <a href="#Category" class="spy_scroll">Category</a>
                        </li>
                        <li>
                            <a href="#Brand" class="spy_scroll">Brand</a>
                        </li>
                        <li>
                            <a href="#Attribute" class="spy_scroll">Attribute</a>
                        </li>
                        <li>
                            <a href="#Units" class="spy_scroll">Units</a>
                        </li>
                        <li>
                            <a href="#AddNewProduct" class="spy_scroll">Add New Product</a>
                        </li>
                        <li>
                            <a href="#BulkProductUpload" class="spy_scroll">Bulk Product Upload</a>
                        </li>
                        <li>
                            <a href="#ProductList" class="spy_scroll">Product List</a>
                        </li>
                        <li>
                            <a href="#InhouseProductList" class="spy_scroll">Inhouse Product List</a>
                        </li>
                        <li>
                            <a href="#RecentViewConfig" class="spy_scroll">Recent View Config</a>
                        </li>
                    </ul>
                </li>

                <li>
                    <a href="#subMenuReviewSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa fa-child"></span>
                        Review
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuReviewSetting">
                        <li>
                            <a href="#ProductReview" class="spy_scroll">Product Review</a>
                        </li>
                        <li>
                            <a href="#SellerReview" class="spy_scroll">Seller Review</a>
                        </li>
                        <li>
                            <a href="#ReviewConfiguration" class="spy_scroll"> Review Configuration</a>
                        </li>
                        <!-- <li>
                            <a href="#MyProductReview" class="spy_scroll">My Product Review</a>
                        </li>
                        <li>
                            <a href="#MyReview" class="spy_scroll">My Review</a>
                        </li> -->
                    </ul>
                </li>

                <li>
                    <a href="#subMenuOrderManageSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa fa-check"></span>
                        Order Manage
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuOrderManageSetting">
                        <li>
                            <a href="#MyOrder" class="spy_scroll">My Order</a>
                        </li>
                        <li>
                            <a href="#TotalOrder" class="spy_scroll">Total Order</a>
                        </li>
                        <li>
                            <a href="#InhouseOrder" class="spy_scroll">Inhouse Order</a>
                        </li>
                        <li>
                            <a href="#DeliveryProcess" class="spy_scroll">Delivery Process</a>
                        </li>
                        <li>
                            <a href="#CancelReason" class="spy_scroll">Cancel Reason</a>
                        </li>
                        <li>
                            <a href="#TrackOrderConfig" class="spy_scroll">Track Order Config</a>
                        </li>
                    </ul>
                </li>

                <li>
                    <a href="#subMenuRefundManageSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <!-- <span class=" fa fa-caret-left"></span> -->
                        <span class="fa-solid fa-arrow-rotate-right"></span>
                        Refund Manage
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuRefundManageSetting">
                        <li>
                            <a href="#PendingRefundRequests" class="spy_scroll">Pending Refund Requests</a>
                        </li>
                        <li>
                            <a href="#ConfirmedRefundRequests" class="spy_scroll">Confirmed Refund Requests</a>
                        </li>
                        <li>
                            <a href="#MyRefundRequests" class="spy_scroll">My Refund Requests</a>
                        </li>
                        <li>
                            <a href="#Reasons" class="spy_scroll">Reasons</a>
                        </li>
                        <li>
                            <a href="#RefundProcess" class="spy_scroll">Refund Process</a>
                        </li>
                        <li>
                            <a href="#RefundConfiguration" class="spy_scroll">Configuration</a>
                        </li>
                    </ul>
                </li>

                <li>
                    <a href="#subMenuSystemSettingsSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa-solid fa-gear"></span>
                        System Settings
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuSystemSettingsSetting">
                        <li>
                            <a href="#GeneralSettings" class="spy_scroll">General Settings</a>
                        </li>
                        <li>
                            <a href="#EmailTemplate" class="spy_scroll">Email Template</a>
                        </li>
                        <li>
                            <a href="#CompanyInformation" class="spy_scroll">Company Information</a>
                        </li>
                        <li>
                            <a href="#SMTPSettings" class="spy_scroll">SMTP Settings</a>
                        </li>
                        <li>
                            <a href="#SMSSettings" class="spy_scroll">SMS Settings</a>
                        </li>
                        <li>
                            <a href="#AnalyticTools" class="spy_scroll">Analytic Tools</a>
                        </li>
                        <li>
                            <a href="#Activation" class="spy_scroll">Activation</a>
                        </li>
                        <li>
                            <a href="#NotificationSetting" class="spy_scroll">Notification Setting</a>
                        </li>
                        <li>
                            <a href="#SocialLoginConfig" class="spy_scroll">Social Login Config</a>
                        </li>
                        <li>
                            <a href="#MaintenanceMode" class="spy_scroll">Maintenance Mode</a>
                        </li>
                        <li>
                            <a href="#AboutUpdate" class="spy_scroll">About & Update</a>
                        </li>
                        <li>
                            <a href="#ModuleManage" class="spy_scroll">Module Manage</a>
                        </li>
                    </ul>
                </li>

                <li>
                    <a href="#subMenuPaymentGatewaysSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa fa-box"></span>
                        Payment Gateways
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuPaymentGatewaysSetting">
                        <li>
                            <a href="#PaymentGateways" class="spy_scroll">Payment Gateways</a>
                        </li>
                    </ul>
                </li>

                <li>
                    <a href="#subMenuSetupSetting" data-toggle="collapse" aria-expanded="false" class="dropdown-toggle">
                        <!-- <span class="fa fa-bullseye"></span> -->
                        <span class="fa-solid fa-wrench"></span>
                        Setup
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuSetupSetting">
                        <li>
                            <a href="#LanguageSettings" class="spy_scroll">Language Settings</a>
                        </li>
                        <li>
                            <a href="#Currency" class="spy_scroll">Currency List</a>
                        </li>
                        <li>
                            <a href="#Tag" class="spy_scroll">Tag</a>
                        </li>
                        <li>
                            <a href="#Location" class="spy_scroll">Location</a>
                        </li>
                        <li>
                            <a href="#ShippingMethod" class="spy_scroll">Shipping Method</a>
                        </li>
                    </ul>
                </li>

                <li>
                    <a href="#subMenuGSTSetupSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <!-- <span class=""></span> -->
                        <span class="fa-solid fa-screwdriver-wrench"></span>
                        GST Setup
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuGSTSetupSetting">
                        <li>
                            <a href="#GSTList" class="spy_scroll">GST List</a>
                        </li>
                        <li>
                            <a href="#GstConfiguration" class="spy_scroll">Configuration</a>
                        </li>
                    </ul>
                </li>

                <li>
                    <a href="#subMenuAccountSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa-solid fa-money-bill"></span>
                        Account
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuAccountSetting">
                        <li>
                            <a href="#ChartOfAccounts" class="spy_scroll">Chart Of Accounts</a>
                        </li>
                        <li>
                            <a href="#BankAccounts" class="spy_scroll">Bank Accounts</a>
                        </li>
                        <li>
                            <a href="#Income" class="spy_scroll">Income</a>
                        </li>
                        <li>
                            <a href="#Expenses" class="spy_scroll">Expenses</a>
                        </li>
                        <li>
                            <a href="#Profit" class="spy_scroll">Profit</a>
                        </li>
                        <li>
                            <a href="#Transaction" class="spy_scroll">Transaction</a>
                        </li>
                        <li>
                            <a href="#Statement" class="spy_scroll">Statement</a>
                        </li>
                    </ul>
                </li>

                <li>
                    <a href="#subMenuSupportTicketSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa-solid fa-ticket-simple"></span>
                        Support Ticket
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuSupportTicketSetting">
                        <li>
                            <a href="#SupportTicket" class="spy_scroll">Support Ticket</a>
                        </li>
                        <li>
                            <a href="#SupportTicketCategory" class="spy_scroll">Category</a>
                        </li>
                        <li>
                            <a href="#Priority" class="spy_scroll">Priority</a>
                        </li>
                        <li>
                            <a href="#Status" class="spy_scroll">Status</a>
                        </li>
                        <!-- <li>
                            <a href="#MyTicket" class="spy_scroll">My Ticket</a>
                        </li> -->
                    </ul>
                </li>

                <li>
                    <a href="#subMenuAllActivityLogsSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa-brands fa-slack"></span>
                        All Activity Logs
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuAllActivityLogsSetting">
                        <li>
                            <a href="#ActivityLogs" class="spy_scroll">Activity Logs</a>
                        </li>
                        <li>
                            <a href="#LoginActivity" class="spy_scroll">Login Activity</a>
                        </li>
                    </ul>
                </li>

                <li>
                    <a href="#subMenuHumanResourceSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa-solid fa-user-check"></span>
                        Human Resource
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuHumanResourceSetting">
                        <li>
                            <a href="#Staff" class="spy_scroll">Staff</a>
                        </li>
                        <li>
                            <a href="#Role" class="spy_scroll">Role</a>
                        </li>
                        <li>
                            <a href="#Department" class="spy_scroll">Department</a>
                        </li>
                        <li>
                            <a href="#Attendance" class="spy_scroll">Attendance</a>
                        </li>
                        <li>
                            <a href="#AttendanceReport" class="spy_scroll">Attendance Report</a>
                        </li>
                        <li>
                            <a href="#Event" class="spy_scroll">Event</a>
                        </li>
                        <li>
                            <a href="#HolidaySetup" class="spy_scroll">Holiday Setup</a>
                        </li>
                        <!-- <li>
                            <a href="#Payroll" class="spy_scroll">Payroll</a>
                        </li>
                        <li>
                            <a href="#PayrollReports" class="spy_scroll">Payroll Reports</a>
                        </li>
                        <li>
                            <a href="#LoanApply" class="spy_scroll">Loan Apply</a>
                        </li>
                        <li>
                            <a href="#LoanHistory" class="spy_scroll">Loan History</a>
                        </li>
                        <li>
                            <a href="#LoanApproval" class="spy_scroll">Loan Approval</a>
                        </li> -->
                    </ul>
                </li>

                <li>
                    <a href="#subMenuVisitorSetupSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa-solid fa-user-clock"></span>
                        Visitor Setup
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuVisitorSetupSetting">
                        <li>
                            <a href="#IgnoreIP" class="spy_scroll">Ignore IP</a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#sidebarManageSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa-solid fa-bars"></span>
                        Sidebar Manage
                    </a>
                    <ul class="collapse list-unstyled" id="sidebarManageSetting">
                        <li>
                            <a href="#SidebarManage" class="spy_scroll">Sidebar Manage</a>
                        </li>
                    </ul>
                </li>

                <li>
                    <a href="#subMenuAdminReportsSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa-solid fa-flag"></span>
                        Admin Reports
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuAdminReportsSetting">
                        <li>
                            <a href="#SellerWiseSales" class="spy_scroll">Seller Wise Sales</a>
                        </li>
                        <li>
                            <a href="#KeywordSearches" class="spy_scroll">Keyword Searches</a>
                        </li>
                        <li>
                            <a href="#Visitor" class="spy_scroll">Visitor</a>
                        </li>

                        <li>
                            <a href="#InhouseProductSale" class="spy_scroll">Inhouse product sale</a>
                        </li>
                        <li>
                            <a href="#ProductStock" class="spy_scroll">Product stock</a>
                        </li>
                        <li>
                            <a href="#Wishlist" class="spy_scroll">Wishlist</a>
                        </li>
                        <li>
                            <a href="#WalletRechargeHistory" class="spy_scroll">Wallet recharge history</a>
                        </li>
                        <li>
                            <a href="#TopSeller" class="spy_scroll">Top seller</a>
                        </li>
                        <li>
                            <a href="#TopCustomer" class="spy_scroll">Top customer</a>
                        </li>
                        <li>
                            <a href="#TopSellingItem" class="spy_scroll">Top selling item</a>
                        </li>
                        <li>
                            <a href="#Order" class="spy_scroll">Order</a>
                        </li>
                        <li>
                            <a href="#Payment" class="spy_scroll">Payment</a>
                        </li>
                        <li>
                            <a href="#AdminProductReview" class="spy_scroll">Product review</a>
                        </li>
                        <li>
                            <a href="#AdminSellerReview" class="spy_scroll">Seller review</a>
                        </li>

                    </ul>
                </li>


                <li>
                    <a href="#subMenuCustomerPanelSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa-solid fa-user-ninja"></span>
                        Customer Panel
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuCustomerPanelSetting">
                        <li><a class="spy_scroll" href="#MyPurchases">My Purchases</a></li>
                        <li><a class="spy_scroll" href="#GiftCard">Gift Card</a></li>
                        <li><a class="spy_scroll" href="#DigitalProduct">Digital Product</a></li>
                        <li><a class="spy_scroll" href="#MyWishlist">My Wishlist</a></li>
                        <li><a class="spy_scroll" href="#RefundDispute">Refund Dispute</a></li>
                        <li><a class="spy_scroll" href="#MyCoupon">My Coupon</a></li>
                        <li><a class="spy_scroll" href="#MyAccount">My Account</a></li>
                        <li><a class="spy_scroll" href="#MyReferral">My Referral</a></li>
                    </ul>
                </li>

                <li>
                    <a href="#subMenuBackupSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa fa-square"></span>
                        <span class="fa-solid fa-backpack"></span>
                        Backup
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuBackupSetting">
                        <li>
                            <a href="#Backup" class="spy_scroll">Backup </a>
                        </li>
                    </ul>
                </li>
                <li>
                    <a href="#subMenuUtilitiesSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="fa fa-square"></span>
                        <span class="fa-solid fa-backpack"></span>
                        Utilities
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuUtilitiesSetting">
                        <li>
                            <a href="#Utilities" class="spy_scroll">Utilities </a>
                        </li>
                    </ul>
                </li>

            </ul>
        </nav>


        <!-- Page Content  -->
        <div id="main-content">
            <nav class="navbar navbar-expand-lg">

            </nav>



            <section class="mb-40" id="initiate">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row border-bottom text-center">
                            <div class="col-lg-12">
                                <h1>Welcome</h1>
                                <h3>To</h3>
                                <h1 class="mb-20">Amazcart ultimate Ecommerce</h1>

                                <h3>Ultimate solution for your ecommerce business</h3>
                                <h1>By</h1>
                                <h1> <a href="https://spondonit.com/" target="blank"> SPONDONIT</a></h1>
                            </div>
                        </div>
                        <div class="row mt-40 border-bottom">
                            <div class="col-lg-12">
                                <p><strong>Email : </strong><a
                                        href="mailto:<EMAIL>"><EMAIL></a><br>
                                    <strong>Web : </strong><a href="https://infixmart.com/" target="blank">
                                        infixmart.com</a>
                                </p>
                            </div>
                        </div>
                        <div class="row mt-40">
                            <div class="col-lg-12">
                                <p>We would like to thank you for purchasing Amazcart Ecommerce System! We are very
                                    pleased you
                                    have chosen Amazcart Ecommerce System for your business, you will not be
                                    disappointed!
                                    Before you get started, please be sure to always check out these documentation
                                    files. We outline all kinds of good information, and provide you with all the
                                    details you need to use Amazcart Ecommerce System. </p>

                                <p>If you are unable to find your answer here in our documentation, watch our Video
                                    Tutorials, you can also visit our Help & Support. Chances are your question or issue
                                    have been brought up already and the answer is waiting to be found. If you are
                                    unable to find it anywhere, then please go our Support section and open a new
                                    Support Ticket with all the details we need. Please be sure to include your site URL
                                    as well. Thank you, we hope you enjoy using Amazcart Ecommerce System!</p>
                            </div>


                            <div class="col-md-10 docs-initiate text-center mt-40">

                                <a class="primary-btn small fix-gr-bg"
                                    href="https://www.youtube.com/watch?v=DhZ6p_tYnpk&list=PLiYjRtR_wL1UPUXpYHjPZGI9qQ-awTQat"
                                    target="blank">Video Tutorials</a>
                                <a class="primary-btn small tr-bg" href="https://ticket.spondonit.com/help-center"
                                    target="blank">Help & Supports</a>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation" id="system-requirements">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">System Requirements</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">

                        <div class="row">
                            <div class="col-lg-12 no-gutters">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart System Requirements</h3>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-lg-12">
                                <p>The Laravel framework has a few system requirements. All of these requirements are
                                    satisfied by the Laravel Homestead virtual machine, so it's highly recommended that
                                    you use Homestead as your local Laravel development environment.</p>
                                <p><b>However, if you are not using Homestead, you will need to make sure your server
                                        meets the following requirements:</b></p>
                                <div class="content-list">
                                    <ul>
                                        <li>PHP >= 7.1. 3</li>
                                        <li>OpenSSL PHP Extension</li>
                                        <li>PDO PHP Extension</li>
                                        <li>Mbstring PHP Extension</li>
                                        <li>Tokenizer PHP Extension</li>
                                        <li>XML PHP Extension</li>
                                        <li>Ctype PHP Extension</li>
                                        <li>JSON PHP Extension</li>
                                        <li>BCMath PHP Extension</li>
                                    </ul>
                                </div>
                                <p>To run the application in your server make sure your php.ini file meets the following
                                    requirements .
                                    Please try change below info on php.ini.</p>
                                <div class="content-list">
                                    <ul>

                                        <li>max_execution_time 30000</li>
                                        <li>max_input_time 30000</li>
                                        <li>memory_limit 512MB</li>
                                        <li>post_max_size 512MB</li>
                                        <li>upload_max_filesize 128MB</li>

                                        <!-- <li>installation_dir/storage/app/</li>
                                        <li>installation_dir/storage/framework/cache</li>
                                        <li>installation_dir/storage/framework/session</li>
                                        <li>installation_dir/storage/framework/testing</li>
                                        <li>installation_dir/public/uploads/</li> -->
                                    </ul>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="system-requirements">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30" id="Installation">Amazcart Installation</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12 text-center">
                                <iframe width="800" height="450" src="https://www.youtube.com/embed/tfQ9htbQIog"
                                    title="YouTube video player" frameborder="0"
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                    allowfullscreen></iframe>
                            </div>
                            <!-- <div class="col-lg-12 text-center">
                                <iframe width="1200" height="675" src="https://www.youtube.com/embed/tfQ9htbQIog"
                                    title="YouTube video player" frameborder="0"
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                    allowfullscreen></iframe>
                            </div> -->
                            <div class="col-lg-12 mt-40">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Database and database user creation on Server</h3>
                                </div>
                            </div>

                            <div class="col-lg-12">
                                <p>Log into your cPanel.</p>
                                <img src="img/installation/cPanelLogin.png" class="img img-responsive cPanelLogin">

                                <p class="mtb">Click the MySQL Database Wizard under the Databases heading.</p>
                                <img src="img/installation/install-dbwizard.png"
                                    class="img img-responsive install-dbwizard">

                                <p class="mtb">Next to New Database enter a name for your database and click Next Step.
                                </p>
                                <img src="img/installation/install-dbwizard2.png"
                                    class="img img-responsive install-dbwizard2">

                                <p class="mtb">Next to Username enter a username.Enter a password next to Password,
                                    enter it again for Password (Again) and then click Create User.</p>
                                <img src="img/installation/install-dbwizard3.png"
                                    class="img img-responsive install-dbwizard3">



                                <p class="mtb">On the next page, you'll assign privileges for the user to the database.
                                    Check the box next to All Privileges and then click Next Step.</p>
                                <img src="img/installation/install-dbwizard4.png"
                                    class="img img-responsive install-dbwizard4">





                                <div class="content-list">
                                    <p>Upload the package to your host</p>
                                    <ul>
                                        <li>Download the package from codecanyon or from Amazcartedu.com Website</li>
                                        <li>Unzip the package and you'll find the following contents
                                            <ul>
                                                <li>Documentation</li>
                                                <li>upload.zip</li>
                                            </ul>
                                        </li>
                                    </ul>

                                    <img src="img/installation/install-files.png"
                                        class="img img-responsive install-files">

                                    <p>Upload file "upload.zip" to your host inside the desired location using cPanel
                                        File Manager</p>
                                    <img src="img/installation/install-filemanager.png"
                                        class="img img-responsive install-filemanager">


                                    <p>After upload is completed, Right click on the package and select Extract. That
                                        will extract the zipped file contents</p>
                                    <img src="img/installation/install-extract.png"
                                        class="img img-responsive install-extract">



                                    <p>Adjust the folders Permissions</p>
                                    <div class="content-list">
                                        <ul>
                                            <li>Change the permissions of uploads folder and all it's contents (Files &
                                                Folders ) to 777</li>
                                            <li>Change the permissions of storage folder and all it's contents (Files &
                                                Folders ) to 777</li>
                                        </ul>
                                    </div>

                                    <p>Start the installation</p>
                                    <div class="content-list">
                                        <ul>
                                            <li>Use your browser to Amazcart install Script . Type in browser your
                                                application location followed by /install. and hit Start Installation
                                            </li>
                                        </ul>
                                    </div>

                                    <img src="img/installation/step1.png" class="img img-responsive step">

                                    <p>Checking your environment for InfixLMS App. If all the requirements look's Fine.
                                        Go to the next step.</p>
                                    <img src="img/installation/step2.png" class="img img-responsive step">

                                    <p>Type your Purchase Code & Envato Account Email for going next step</p>
                                    <img src="img/installation/step3.png" class="img img-responsive step ">

                                    <p>Give Database name, Database user name & Database password. And go next step.
                                    </p>

                                    <img src="img/installation/step4.png" class="img img-responsive step ">

                                    <p>Here some basic info for the admin setup. You can change it after installation.
                                    </p>

                                    <img src="img/installation/step5.png" class="img img-responsive step ">

                                    <p>Installation complete</p>
                                    <img src="img/installation/step6.png" class="img img-responsive step ">

                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </section>

            
            <section class="infix-documentation mt-40" id="GetSupport">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Installation Note For Local Server</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <p>To run the application in localhost make sure your php.ini file meets the following
                                    requirements .<br>
                                    Please try change below info on php.ini
                                </p>
                                <p>

                                    max_execution_time 30000<br>
                                    max_input_time 30000<br>
                                    memory_limit 512MB<br>
                                    post_max_size 512MB<br>
                                    upload_max_filesize 128MB<br><br>
                                    Also, make sure PHP -v see in command line min version 7.4.x

                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="GetSupport">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Get Support</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Get Support from Amazcart</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <p>To get Technical/Sales support. You must create Amazcart Account first.</p>
                                <div class="row">
                                    <div class="col-sm-8">
                                        <h3>Contact Info</h3>
                                        <p style="margin: 0px !important;"> <b>Email:</b> <a
                                                href="mailto:<EMAIL>"> <EMAIL></a></p>
                                        <p style="margin: 0px !important;"> <b>Web:</b> <a
                                                href="https://infixmart.com/">infixmart.com</a></p>
                                        <p style="margin: 0px !important;"> <b>Support:</b> <a
                                                href="https://ticket.spondonit.com/help-center">ticket.spondonit.com</a>
                                        </p>


                                    </div>
                                    <div class="col-sm-4">
                                        <img src="img/support.png" class="img img-responsive" style="width: 100%">
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Dashboard -->
            <section class="infix-documentation mt-40" id="Dashboard">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Dashboard</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Dashboard</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img class="img img-responsive dashboard1" src="img/dashboard/newdashboard.png">
                                <h3 class="mt-40">1. Dashboard</h3>
                                <p>At the top menu of left sidebar is Dashboard. There are several view port in
                                    dashboard. we can see the number of products, seller, customer etc. at a glance.
                                    Summary of the system is important. After login
                                    admin can see the update for that day.</p>

                                <h3 class="mt-40">2. Toggle Bar</h3>
                                <p>To hide and show the side menu bar.</p>
                                <h3 class="mt-40">3. Website</h3>
                                <p>The button lead you to the main website. </p>

                                <h3 class="mt-40">4. Language</h3>
                                <p> Amazcart delivered to you equiped with diffrent languages : English and
                                    Bangla. You can also add laguage with your own customization.</p>

                                <h3 class="mt-40">5. Quick Menu</h3>
                                <p> Most important quick menu are there so that admin can find easily. </p>

                                <h3 class="mt-40">6. Knowledge Base </h3>
                                <p> This link can lead the admin to Knowledge Base. </p>

                                <h3 class="mt-40">7. Notification</h3>
                                <p>Different types of notifications are created in the system. You can find it here.
                                </p>

                                <h3 class="mt-40">8. Profile</h3>
                                <p>Admin can see their profile, change their existing password, update profile
                                    setting etc. Logout button at the
                                    bottom of this profile section. </p>


                                <h3 class="mt-40">9. Seller Dashboard</h3>
                                <p>Go to your seller dashboard panel. </p>
                                <h3 class="mt-40">10. Today</h3>
                                <p>When admin login to the system he shows the todays summary. </p>

                                <h3 class="mt-40">11. This Week</h3>
                                <p>By clicking this week button admin can show this week summary. </p>
                                <h3 class="mt-40">12. This Month</h3>
                                <p>By clicking this month button admin can show this month summary. </p>
                                <h3 class="mt-40">13. This Year</h3>
                                <p>By clicking this year button admin can show this year summary. </p>





                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Different Summaries In Chart</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <img class="img img-responsive dashboard1 mb-40" src="img/dashboard/dashboard2.png">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Top Product and Seller List</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <img class="img img-responsive dashboard1 mb-40" src="img/dashboard/dashboard3.png">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Category Wise Product and Product Sale List</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <img class="img img-responsive dashboard1 mb-40" src="img/dashboard/dashboard4.png">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Coupon Wise Sale and New Customer List</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <img class="img img-responsive dashboard1 mb-40" src="img/dashboard/dashboard5.png">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Recently Added Product and Top Refferer List</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <img class="img img-responsive dashboard1 mb-40" src="img/dashboard/dashboard6.png">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Latest Order and Keyword Search List</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <img class="img img-responsive dashboard1 mb-40" src="img/dashboard/dashboard7.png">
                                <p>Admin can also show order details from here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Top Customers & Recent Reviews</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <img class="img img-responsive dashboard1" src="img/dashboard/dashboard9.png">
                                <h3 class="mt-40">15. Top Customer</h3>
                                <p>View top customer.</p>
                                <h3 class="mt-40">16. Top Seller</h3>
                                <p>View top seller.</p>
                                <h3 class="mt-40">17. Product Reviews</h3>
                                <p>View the reviews of the products.</p>
                                <h3 class="mt-40">17. Seller Reviews</h3>
                                <p>View the reviews of the Seller.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>


            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Personal Notification Setting</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <img class="img img-responsive dashboard1 mb-40" src="img/user_notification.png">
                                <h3 class="mt-40">Personal Notification Setting</h3>
                                <p>You can set up which notification you want to get.</p>
                                <h3 class="mt-40">1.Setting</h3>
                                <p>Lead you to the notification setting page.</p>
                                <h3 class="mt-40">2.Read All</h3>
                                <p>This will mark the notification as read.</p>
                                <h3 class="mt-40">3.View</h3>
                                <p>View all notifications.</p>

                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Frontend CMS -->
            <section class="infix-documentation mt-40" id="Homepage">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Frontend CMS</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Homepage</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/frontendcms/homepage.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Home Page</h3>
                                <p>This is the landing page of the website. You can customize this page according to
                                    your wish.</p>
                                <h3 class="mt-40">1 & 2. Select List</h3>
                                <p>This section describe how the home page will show. To change the view of the website
                                    you can change it from here. Different selection options are
                                    here. Product list changes according to the selection. Admin can enable and disable
                                    any section. Disabled section will be hide from the main website.</p>
                                <h3 class="mt-40">3. Update</h3>
                                <p>Admin can change title, column size and type. If you want to change the home page
                                    column from 6 to 4 , you can update the column size here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="Features">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Features</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Features</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/frontendcms/features.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Features</h3>
                                <p>Features describe what your website offers. You can visually represent the features
                                    so that users can easily understand what you website offers.
                                    You can find the website view at the bottom of any page.</p>
                                <h3 class="mt-40">1. Save Feature</h3>
                                <p>Admin can add new feature. Active features are shown in the website.</p>
                                <h3 class="mt-40">2. Feature List</h3>
                                <p>Feature List is shown here. </p>
                                <h3 class="mt-40">3. Quick Search</h3>
                                <p>Easily search the table by entering any key. </p>
                                <h3 class="mt-40">4. Icons</h3>
                                <p>The icons are used for copy,csv,excel,print,pdf all the data from the table. </p>
                                <h3 class="mt-40">5. Action</h3>
                                <p>Feature can be edit or delete from here. </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Features Website View</h3>
                            </div>
                        </div>
                        <!-- <div class="white-box "> -->
                        <div class="col-lg-12">
                            <img src="img/frontendcms/features2.png"
                                class="img img-responsive general-setting documentationImg img-border">

                            <!-- </div> -->

                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="Merchent">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Merchent Content</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Merchent Content</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/frontendcms/merchant-content.png"
                                    class="img img-responsive general-setting documentationImg ">

                                <h3 class="mt-40">Merchant Content</h3>
                                <p>This is the merchant page for regestration. You can customize this page. You can find
                                    the page here <a href="#">https://yourdomain.com/merchant</a>.</p>
                                <h3 class="mt-40">1. Add Benifit List</h3>
                                <p>Admin can add new Benifit List. Benifit List are shown in the merchant page.</p>
                                <h3 class="mt-40">2. Benifit Action</h3>
                                <p>Benifit List can edit or delete. </p>
                                <h3 class="mt-40">3. Add How It Work List</h3>
                                <p>Admin can add new How It Work List. How It Work List are shown in the merchant page.
                                </p>
                                <h3 class="mt-40">4. Benifit Action</h3>
                                <p>Benifit List can edit or delete. </p>
                                <h3 class="mt-40">5. Add Frequently Asked Question List</h3>
                                <p>Admin can add new Frequently Asked Question List. Frequently Asked Question List are
                                    shown in the merchant page.</p>
                                <h3 class="mt-40">6. Frequently Asked Question Action</h3>
                                <p>Frequently Asked Question List can edit or delete. </p>
                                <h3 class="mt-40">7. Update</h3>
                                <p>All the details of the merchant can be updated.. </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Merchent Content Website View</h3>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <img src="img/frontendcms/merchant-content-web.png"
                                class="img img-responsive general-setting documentationImg mb-40">
                        </div>
                    </div>
                </div>
            </section>


            <section class="infix-documentation mt-40" id="ReturnExchange">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Return & Exchange</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Return & Exchange</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/frontendcms/return.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Return & Exchange</h3>
                                <p>If any user wants to return any product he/she has to follow the return and exchange
                                    policies. You can find the page here <a
                                        href="#">https://yourdomain.com/return-exchange </a> .</p>
                                <h3 class="mt-40">1. Update</h3>
                                <p>Admin can update Return & Exchange . (<span style="color:red">*</span>) marked fields
                                    are required</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Return & Exchange Website View</h3>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <img src="img/frontendcms/return2.png"
                                class="img img-responsive general-setting documentationImg">
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="ContanctContent">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Contanct Content</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Contact Content</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/frontendcms/contact-content3.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Contact Content</h3>
                                <p>Setup your "contact us" page for users. You can find the page here <a
                                        href="#">https://yourdomain.com/contact-us </a> .</p>
                                <h3 class="mt-20">1. Update</h3>
                                <p>Admin can update Contact Content. All the fields are required. </p>
                                <h3 class="mt-20">2. Save</h3>
                                <p>Admin can add edit and delete inquery.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Contact Content Website View</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <img src="img/frontendcms/contact-content2.png"
                                    class="img img-responsive general-setting documentationImg ">

                                <!-- <img src="img/frontendcms/contact-content4.png"
                                    class="img img-responsive general-setting "> -->
                            </div>
                        </div>
                    </div>
                </div>
            </section>


            <section class="infix-documentation mt-40" id="DynamicPages">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Dynamic Pages</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Dynamic Pages</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/frontendcms/dynamicpage.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Dynamic Page</h3>
                                <p>Dynamic pages are not directly shown in the
                                    website. When you add any dynamic page you can find it in many section like footer,
                                    menu etc where you can add the page.</p>
                                <h3 class="mt-40">1. Add New</h3>
                                <p>Dynamic Pages can be added from here. </p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Admin can view, edit and delete the dynamic pages. </p>

                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="DynamicPages">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Dynamic Page (Create)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">

                                <img src="img/frontendcms/dynamicpage2.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>



            <section class="infix-documentation" id="FooterSetting">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Footer Setting</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Footer Setting (Copyright)</h3>
                                    <img src="img/frontendcms/footersetting1.png"
                                        class="img img-responsive general-setting documentationImg">
                                    <h3 class="mt-40">Footer Setting</h3>
                                    <p>You can dynamically change your footer contents. It is the bottom part of the
                                        website.</p>
                                    <h3 class="mt-40">1. Copyright Text</h3>
                                    <p>Admin can update copyright text content.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Footer Setting (About)</h3>
                                    <img src="img/frontendcms/footersetting2.png"
                                        class="img img-responsive general-setting documentationImg">
                                    <h3 class="mt-40">2. About Text</h3>
                                    <p>Admin can update about text content.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Footer Setting (Company)</h3>
                                    <img src="img/frontendcms/footersetting3.png"
                                        class="img img-responsive general-setting documentationImg mt-40">
                                    <h3 class="mt-40">3. Company</h3>
                                    <p>Admin can add new page and update the section name "Company".</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Footer Setting (My Account)</h3>
                                    <img src="img/frontendcms/footersetting4.png"
                                        class="img img-responsive general-setting documentationImg mt-40">
                                    <h3 class="mt-40">4. My Account</h3>
                                    <p>Admin can add new page and update the section name "My Account".</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Footer Setting (Services)</h3>
                                    <img src="img/frontendcms/footersetting5.png"
                                        class="img img-responsive general-setting documentationImg mt-40">
                                    <h3 class="mt-40">5. Services</h3>
                                    <p>Admin can add new page and update the section name "Services"</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation" id="">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Footer Setting Website View</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <img src="img/frontendcms/footersetting6.png"
                                        class="img img-responsive general-setting documentationImg mt-20">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="SubscribeContent">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Subscribe Content</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Subscribe Content</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/frontendcms/subscribecontent.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Subscribe Content</h3>
                                <p>You can update the subscribe content text.You can find it in the bottom of the
                                    website.</p>
                                <h3 class="mt-40">1. Update</h3>
                                <p>Subscribe Content can be updated from here. All the fields are required.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="SubscribeContent">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Subscribe Content Website View</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <img src="img/frontendcms/subscribecontent1.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="AboutUs">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">About Us</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart About Us</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/frontendcms/aboutus.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">About Us</h3>
                                <p>This is the about us page details. You can update your about us page information from
                                    here. You can find the page here <a href="#">https://yourdomain.com/about-us </a>
                                </p>
                                <h3 class="mt-40">1. Update</h3>
                                <p>Admin can update about us page Content. All the fields are required.</p>

                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="SubscribeContent">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">About Us Website View</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <img src="img/frontendcms/aboutus2.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="Title">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Related Sale Setting</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Related Sale Setting</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/frontendcms/title3.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Related Sale Setting</h3>
                                <p>There are two types of product. Up sale product and cross sale product. You can
                                    change the title of this two type product title.</p>
                                <h3 class="mt-40">1. Save</h3>
                                <p>Admin can update up Sale Product and Cross Sale Product title. </p>

                            </div>
                        </div>
                    </div>
                </div>
            </section>


            <!-- Apperence -->
            <section class="infix-documentation mt-40" id="Themes">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Themes</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Themes</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/apperence/themes.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Themes</h3>
                                <p>Themes represent how the website will look. You can add new theme.</p>
                                <h3 class="mt-40">1. Add Theme</h3>
                                <p>Admin can add new theme from here. A zip file will be uploaded for theme. </p>


                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Upload Theme</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <img src="img/apperence/themes2.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="ColorScheme">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Color Scheme</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Color Scheme</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/apperence/color_scheme.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Color Scheme</h3>
                                <p>You can change the color of the website.</p>
                                <h3 class="mt-40">1. Update</h3>
                                <p>Update the color scheme. </p>
                                <h3 class="mt-40">2. Activate</h3>
                                <p>Activate the color scheme. </p>


                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="Menus">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Menus</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Menus</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/apperence/menus.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Menus</h3>
                                <p>There are different types of menus in the website. You can manage and customize the
                                    menus from here.</p>
                                <h3 class="mt-40">1. Save</h3>
                                <p>Admin can add menu from here.(<span style="color:red">*</span>) marked fields are
                                    required. Active menus show in the website sidebar. </p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Admin can edit or delete menu from here. Menus are rearrengable. To rearreng a menu
                                    use
                                    mouse click and drag. Menu setup is also done from here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="Menus">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Menu Setup</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/apperence/menus2.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Menu Setup</h3>
                                <p>Menu setup has 3 part. Column, Right panel and Bottom Panel.Column can be added in 3
                                    different size. These are 1/1, 1/2, 1/3. In every column Links, Categories, Pages,
                                    Products, Brands, Tags can be added. When you add any of the item it will go to the
                                    Menu Item List section. After that you can drag the item to a specific column. The
                                    item are dragable, editable and deletable. </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="Menus">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Menu Setup (Right Panel)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/apperence/menus3.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="Menus">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Menu Setup (Bottom Panel)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/apperence/menus4.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="Menus">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Menus Website View</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <img src="img/apperence/menuweb.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="Header">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Header</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Header Setup</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/apperence/header.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Header Setup</h3>
                                <p>You can customize the header section of the landing page. You can enable or disable
                                    any section and setup according to your convenience. </p>
                                <h3 class="mt-40">1. Status</h3>
                                <p>Status can be updated from here. Inactive section will not show in the website. </p>

                                <h3 class="mt-40">2. Action</h3>
                                <p>A section can be setup from the action. Section update, change is possible from here.
                                </p>
                            </div>
                            <div class="col-lg-12">



                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Dashboard Setup -->
            <section class="infix-documentation mt-40" id="DashboardSetup">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Dashboard Setup</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Dashboard Setup</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setup/dashboard.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Dashboard Setup</h3>
                                <p>You can customize your dashboard view from here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="Menus">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="col-lg-12">
                                    <div class="main-title">
                                        <h3 class="mb-30 text-center">Amazcart Header Setup</h3>
                                    </div>
                                </div>
                                <img src="img/apperence/header2.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. update</h3>
                                <p>Column size can be changed from here. </p>
                                <h3 class="mt-40">2. Setup</h3>
                                <p> Section setup can be done from here. </p>

                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="Menus">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="col-lg-12">
                                    <div class="main-title">
                                        <h3 class="mb-30 text-center">Amazcart Header Setup</h3>
                                    </div>
                                </div>
                                <img class="img img-responsive general-setting documentationImg"
                                    src="img/apperence/header3.png" />
                                <h3 class="mt-40">1. Save</h3>
                                <p>New item can be added to the list. </p>
                                <h3 class="mt-40">2. List</h3>
                                <p> The items in the list are rearrengable. Items can be rearrenged by click and drag
                                    the specific item. </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="Menus">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Header Website view</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <img src="img/apperence/header4.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="DashboardColor">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Dashboard Color</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Dashboard Color</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/apperence/dashboard_color.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Dashboard Color</h3>
                                <p>You can change the color of admin panel.</p>
                                <h3 class="mt-40">1. Add new</h3>
                                <p>Add new color for your admin panel. </p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>You can clone the base color. Edit and delete it. </p>

                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="Menus">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="col-lg-12">
                                    <div class="main-title">
                                        <h3 class="mb-30 text-center">Amazcart Dashboard Color (Add Dashboard Color)
                                        </h3>
                                    </div>
                                </div>
                                <img src="img/apperence/dashboard_color2.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Blog -->
            <section class="infix-documentation mt-40" id="Blog">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Blog</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Blog</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/blog/blog.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Blog</h3>
                                <p>Blogs are important for SEO. You can write blog post about your products or any other
                                    topic. You can find the blog page from here <a
                                        href="#">https://yourdomain.com/blog</a> .</p>
                                <h3 class="mt-40">1. Add </h3>
                                <p>New blog post can be added from here. Category and tag have to create first from Blog
                                    Category and Blog Tags option.</p>
                                <h3 class="mt-40">2. Action </h3>
                                <p>Blog can edit or delete by admin. Status can be toggle.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="Menus">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="col-lg-12">
                                    <div class="main-title">
                                        <h3 class="mb-30 text-center">Amazcart Blog (Create Blog Post)</h3>
                                    </div>
                                </div>
                                <img src="img/blog/blog2.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Blogs Website View</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <img src="img/blog/blog3.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="BlogCategory">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Blog Category</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Blog Category</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/blog/blogcategory.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Blog Category</h3>
                                <p>When you want to add a blog post you have to select a category for the post. You can
                                    manage the blog category from here.</p>
                                <h3 class="mt-40">1. Add </h3>
                                <p>Add blog category here. A category can be a parent or child. Blog categories are used
                                    in creating Blog Post.</p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Blog category can be edit or delete by Admin.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Customer -->
            <section class="infix-documentation mt-40" id="Customer">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Customer</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Customer</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customer/customer.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Customer</h3>
                                <p>Anyone buy any product from your website is your customer. You can active and
                                    deactive them. Deactive customer cannot login to the website. </p>
                                <h3 class="mt-40">1. Active Customer </h3>
                                <p>All register users are Active customers. Active customer can be make inactive
                                    customer by togging the IS ACTIVE.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="Customer">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Customer</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <h3 class="mt-40 mb-20">2. In Active Customer List</h3>
                                <img src="img/customer/customer2.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Seller -->
            <section class="infix-documentation mt-40" id="SellerList">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Active Seller List</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Active Seller List</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/seller/sellerlist.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Seller List</h3>
                                <p>A merchant is a seller. He/She can sell his/her product. You can see the list of all
                                    seller in your website. You can also performe multiple action.</p>
                                <h3 class="mt-40">1. Add Seller </h3>
                                <p>Add new seller to the system.</p>
                            </div>
                            <div class="col-lg-12">
                                <h3 class="mt-40">2. Action </h3>
                                <p>Seller details can be found here. You can secretly login to this seller account and
                                    make him trusted.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Seller (Create)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/seller/sellerlist2.png"
                                    class="img img-responsive general-setting documentationImg">

                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Seller (Details)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/seller/sellerd.png"
                                    class="img img-responsive general-setting documentationImg">

                            </div>
                            <div class="col-lg-12">
                                <h3 class="mt-40">1. Edit </h3>
                                <p>Seller details can be edited from here. </p>
                                <h3 class="mt-40">2. Edit Commission Rate </h3>
                                <p>Commission Rate of a Seller can be edited from here. </p>
                                <h3 class="mt-40">3. Make Trusted </h3>
                                <p>Seller can be maked as trusted. </p>
                                <h3 class="mt-40">4. Orders </h3>
                                <p>Seller Orders list. </p>
                                <h3 class="mt-40">5. Wallet History </h3>
                                <p>Seller Wallet History list. </p>
                                <h3 class="mt-40">6. Products </h3>
                                <p>Seller Products list. </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Seller (Profile)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/seller/sellerlist4.png"
                                    class="img img-responsive general-setting documentationImg">

                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Seller (Profile)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/seller/sellerlist5.png"
                                    class="img img-responsive general-setting documentationImg">

                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Seller (Profile)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/seller/sellerlist6.png"
                                    class="img img-responsive general-setting documentationImg">

                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Seller (Profile)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/seller/sellerlist7.png"
                                    class="img img-responsive general-setting documentationImg">

                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Seller (Profile)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/seller/sellerlist8.png"
                                    class="img img-responsive general-setting documentationImg">

                            </div>
                        </div>
                    </div>
                </div>
            </section>



            <section class="infix-documentation mt-40" id="InactiveSeller">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Inactive/Request Seller List</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Inactive/Request Seller List</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/Seller/inactive_seller.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Inactive/Request Seller List</h3>
                                <p>Inactive seller cannot login to the system. You can find the inactive seller here.
                                </p>
                                <h3 class="mt-40">1. Action </h3>
                                <p>Admin can performe multiple action here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="ComissionSetup">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Comission Setup</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Comission Setup</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/Seller/comission.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Comission Setup</h3>
                                <p>When any seller sells any product or subscribe to the system you can get comission
                                    from it. You can setup the comission here.</p>
                                <h3 class="mt-40">1. Update </h3>
                                <p>Admin can update comission details.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="PricingPlan">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Pricing Plan</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Pricing Plan</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/frontendcms/pricing-plan.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Pricing Plan</h3>
                                <p>When any user want to be a merchant he/she has to choose a pricing plan. You can find
                                    the page here <a href="#">https://yourdomain.com/merchant</a> .</p>
                                <h3 class="mt-40">1. Save</h3>
                                <p>Admin can add new pricing plan. (<span style="color:red">*</span>) marked fields are
                                    required. Merchant can choose any pricing plan.</p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Pricing plan list can be edit and delete from here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Pricing plan Website View</h3>
                            </div>
                        </div>
                        <div class="col-lg-12">
                            <img src="img/frontendcms/pricing-plan2.png"
                                class="img img-responsive general-setting documentationImg">
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="SubscriptionPayment">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Subscription Payment</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Subscription Payment</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/Seller/subscription.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Subscription Payment</h3>
                                <p>When any seller subscribe to the system he/she has to pay. Here you can see the list
                                    of that payment.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="AutoApproveSellerConfiguration">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Auto Approve Seller Configuration</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Auto Approve Seller Configuration</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/Seller/config.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Auto Approve Seller Configuration</h3>
                                <p>When auto approve seller is active, seller are automatically approved to the system
                                    and can sell the product otherwise admin will manually activate the seller from
                                    inactive/request seller list.</p>
                                <h3 class="mt-40">1. Update </h3>
                                <p>Admin can update the setting..</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- My Wallet -->
            <section class="infix-documentation mt-40" id="MyWallet">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Transaction</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Transaction</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/wallet/mywallet.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Transaction</h3>
                                <p>This is your personal wallet. You can recharge to it and buy anything from the
                                    website using wallet balance.</p>
                                <h3 class="mt-40">1. Recharge </h3>
                                <p>Admin can recharge from here.</p>
                                <h3 class="mt-40">2. Withdraw </h3>
                                <p>Admin can withdraw from here.</p>

                            </div>
                        </div>
                    </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Wallet (Recharge)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                            </div>
                            <div class="col-lg-12">
                                <img src="img/wallet/mywallet2.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Continue </h3>
                                <p>Recharge process will start after pressing continue button.</p>

                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Wallet (Withdraw)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/wallet/mywallet3.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Send </h3>
                                <p>Withdraw request will be sent.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="Withdraw">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Withdraw</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Withdraw</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/wallet/withdraw.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Withdraw</h3>
                                <p>You can see the withdraw requests from the seller. </p>
                                <h3 class="mt-40">1. Edit </h3>
                                <p>Withdraw details can be updated.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Wallet Manage -->
            <section class="infix-documentation mt-40" id="OnlineRecharge">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Online Recharge</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Online Recharge</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/wallet-manage/online_recharge.png"
                                    class="img img-responsive general-setting documentationImg">

                            </div>
                        </div>
                    </div>
            </section>
            <section class="infix-documentation mt-40" id="BankRecharge">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Bank Recharge</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Bank Recharge</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/wallet-manage/bank_recharge.png"
                                    class="img img-responsive general-setting documentationImg">

                            </div>
                        </div>
                    </div>
            </section>

            <!-- <section class="infix-documentation mt-40" id="WalletRechargeApproval">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Wallet Recharge Approval</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Wallet Recharge Approval</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/wallet-manage/1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Wallet Recharge Approval</h3>
                                <p>You can see the approved wallet recharge.</p>
                                <h3 class="mt-40">1. Offline Recharge </h3>
                                <p>Admin can recharge seller in offline from here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="WalletRechargeApproval">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Offline Recharge</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/wallet-manage/2.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section> -->

            <section class="infix-documentation mt-40" id="WithdrawRequest">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Withdraw Request</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Withdraw Request</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/wallet-manage/3.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Withdraw Request</h3>
                                <p>You can see the withdraw requests from the seller. </p>
                                <h3 class="mt-40">1. Show Details </h3>
                                <p>Admin can show the full details.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="WithdrawRequest">
                <div class="container-fluid p-0">

                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Withdraw Request Details</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/wallet-manage/4.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="OfflineRecharge">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Offline Recharge</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Offline Recharge</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/wallet-manage/5.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Offline Recharge</h3>
                                <p>You can see the offline recharge history of the sellers. </p>
                                <h3 class="mt-40">1. Approve </h3>
                                <p>Admin can approve offline recharges.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="WalletConfiguration">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Auto Approve Wallet Configuration</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Auto Approve Wallet Configuration</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/wallet-manage/config.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Update </h3>
                                <p>Admin can update wallet configuration.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Contact Mail -->
            <section class="infix-documentation mt-40" id="ContactMail">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Contact Mail</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Contact Mail</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/contact-mail/1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Contact Mail</h3>
                                <p>Whoever sends any message to contact you, you can find it here.</p>
                                <h3 class="mt-40">1. Delete </h3>
                                <p>Admin can delete contact mail.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Flash Deals -->
            <section class="infix-documentation mt-40" id="FlashDeals">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Flash Deals</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Flash Deals</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/marketing/2.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Flash Deals</h3>
                                <p>Flash deals are special types of offer which in a short time.Flash deal page can be
                                    found in a specific url. When you create a flash deal a page link is also created.
                                    Through this link you and other users can access to the deals. Flash deals status
                                    shold be on to access the page.</p>
                                <h3 class="mt-40">1. Add New </h3>
                                <p>Admin can add new flash deals. Flash will show in the website if the status is on.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Flash Deals (Create)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/marketing/1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Save </h3>
                                <p>Save flash deals.</p>
                                <h3 class="mt-40">2. Delete </h3>
                                <p>Product can be removed from the list.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Flash Deals Website View</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <img src="img/marketing/3.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Coupons -->
            <section class="infix-documentation mt-40" id="Coupons">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Coupons</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Coupons</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/marketing/coupon1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Coupons</h3>
                                <p>Through coupon user can get discount when buying any product. It is a great way for
                                    selling the product more. When any user go to the checkout page for ordering any
                                    product he/she can see a field to enter a coupon. If the coupon is valid he/she get
                                    the discount which is set by you. </p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Admin can add new coupons. Different types of coupon can be added. </p>
                                <h3 class="mt-40">2. Action </h3>
                                <p>Coupon can edit or delete</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- New User Zone -->
            <section class="infix-documentation mt-40" id="NewUserZone">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">New User Zone</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart New User Zone</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/marketing/newuser1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">New User Zone</h3>
                                <p>You can find the new user zone in the website landing page. When you click to the
                                    link you redirect to a page. You can customize this page for new user. You can add
                                    some extra benifits for the new users. </p>
                                <h3 class="mt-40">1. Add New </h3>
                                <p>Admin can add new user zone. This will show in the website if the status is on.</p>
                                <h3 class="mt-40">2. Action </h3>
                                <p>User zone can edit or delete</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="NewUserZone">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart New User Zone (Create)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/marketing/newuser2.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="NewUserZone">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart New User Zone (Create)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/marketing/newuser3.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="NewUserZone">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart New User Zone (Create)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/marketing/newuser4.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="NewUserZone">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart New User Zone (Create)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/marketing/newuser5.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">3. Save</h3>
                                <p>Save the user zone.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">New User Zone Website View</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <img src="img/marketing/newuser6.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="NewsLetter">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">News Letter</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart News Letter</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">

                                <img src="img/marketing/newsletter.png"
                                    class="img img-responsive general-setting documentationImg">

                                <h3 class="mt-40">1. Add New</h3>
                                <p>Add new NewsLetter</p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Newsletter can edit and delete from here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="BulkSms">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Bulk Sms</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Bulk Sms</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/marketing/bulksms1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Bulk SMS</h3>
                                <p>You can send sms to the customer or seller.</p>
                                <h3 class="mt-40">1. Save</h3>
                                <p>Create bulk sms from here. Admin can select multiple user to send sms</p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Create bulk can edit and delete from here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="Subscription">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Subscription</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Subscription</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/marketing/subscriber.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Subscription</h3>
                                <p>You can see the subscriber list who are subscribed to the news letter.</p>
                                <h3 class="mt-40">1. Action</h3>
                                <p>Subscriber can delete from here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="ReferralCodeSetup">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Referral Code Setup</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Referral Code Setup</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/marketing/referral.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Referral Code Setup</h3>
                                <p>User can refer to other user through referral code. User can use referral code while
                                    regestration. When any user uses any referral code, referer user get some benifits.
                                    You can customze the benifits and codes from here. </p>
                                <h3 class="mt-40">1. Update</h3>
                                <p>Referral code can update from here.</p>
                                <h3 class="mt-40">2. Status</h3>
                                <p>Referral code status can be changed.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="GiftCardList">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Gift Card List</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Gift Card List</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/gift-card/0.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Gift Card</h3>
                                <p>User can buy gift card as product. Using the gift card user can recharge their
                                    wallet. You can find the gift card from here <a
                                        href="#">https://yourdomain.com/gift-cards</a> .</p>
                                <h3 class="mt-40">1. Add New</h3>
                                <p>Add new gift card from here.</p>
                                <h3 class="mt-40">2. Bulk Upload</h3>
                                <p>Bulk upload the gift cards.</p>
                                <h3 class="mt-40">3. Action</h3>
                                <p>Gift card can view, edit and delete.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Gift Card (Create)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/gift-card/1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Save</h3>
                                <p>Save the gift card.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Gift Card (Bulk Upload)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/gift-card/2.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Upload</h3>
                                <p>Upload the csv file.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="Category">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Category</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Category</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/products/category1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Category</h3>
                                <p>When you add a prodcut you have to select a category and in menu setup you have to
                                    select category. You can manage category here. You can find the added categories in
                                    many section of this panel. </p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Add new category from here.</p>
                                <h3 class="mt-40">2. Bulk Category Upload </h3>
                                <p>Admin can add many categories at a time.</p>
                                <h3 class="mt-40">3. Category CSV</h3>
                                <p>Admin can download the categories in csv file.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Category Bulk Upload</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/products/category2.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="Brand">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Brand</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Brand</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/products/brand1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Brand</h3>
                                <p>You can manage the brands here. Brands are shown in the website in different
                                    sections.</p>
                                <h3 class="mt-40">1. Add </h3>
                                <p>Add new brand from here.</p>
                                <h3 class="mt-40">2. Bulk </h3>
                                <p>Bulk upload the brands.</p>
                                <h3 class="mt-40">3. Brand CSV</h3>
                                <p>Admin can download the brands in csv file.</p>
                                <h3 class="mt-40">4. Load More</h3>
                                <p>Load more brand to view.</p>
                                <h3 class="mt-40">5. Action</h3>
                                <p>Admin can edit and delete brand.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>


            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Brand (Create)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/products/brand2.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Save</h3>
                                <p>Save the brand.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Brand Bulk Upload</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/products/brand3.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="Attribute">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Attribute</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Attribute</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/products/attribute.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Save </h3>
                                <p>Add new attribute from here.</p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Admin can edit and delete attribute.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="Units">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Units</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Units</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/products/unit.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Units</h3>
                                <p>Product has different types of units. You can manage it from here.</p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Add new unit from here.</p>
                                <h3 class="mt-40">2. Units CSV </h3>
                                <p>Download the units in csv file.</p>
                                <h3 class="mt-40">3. Action</h3>
                                <p>Admin can edit and delete the units.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="AddNewProduct">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Add New Product</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Add New Product</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/products/addnewproduct1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Add New Product</h3>
                                <p>When you add any new product , this product does not show in the website directly.
                                    The product should be under a seller. Seller can add this product for sale in their
                                    panel. </p>
                                <h3 class="mt-40">1. General Info </h3>
                                <p>General information of the product.</p>
                                <h3 class="mt-40">2. Save </h3>
                                <p>Admin can add product from here. Admin has to give all the necessary information.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Add New Product</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/products/addnewproduct2.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Add New Product</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/products/addnewproduct3.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Add New Product</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/products/addnewproduct4.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="BulkProductUpload">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Bulk Product Upload</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Bulk Product Upload</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/products/bulkproductupload.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Bulk Product Upload</h3>
                                <p>You can upload many product at a time. For that you have to download the csv file and
                                    prepare your product list according to the csv file.</p>
                                <h3 class="mt-40">1. Upload CSV </h3>
                                <p>Upload product csv file.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="ProductList">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Product List</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Product List</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/products/productlist1.png"
                                    class="img img-responsive general-setting documentationImg">

                                <h3 class="mt-40">Product List</h3>
                                <p>You can find all the product list here. </p>
                                <h3 class="mt-40">1. Product List </h3>
                                <p>Product list is shown here.</p>
                                <h3 class="mt-40">2. Add New Product </h3>
                                <p>Admin can add new product.</p>
                                <h3 class="mt-40">3. Action</h3>
                                <p>Admin can view, edit and delete the product.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>


            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Product List</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/products/productlist2.png"
                                    class="img img-responsive general-setting documentationImg">

                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Product List</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/products/productlist3.png"
                                    class="img img-responsive general-setting documentationImg">

                            </div>
                        </div>
                    </div>
                </div>
            </section>


            <section class="infix-documentation mt-40" id="InhouseProductList">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Inhouse Product List</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Inhouse Product List</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/products/inhouseproduct1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Inhouse Product List</h3>
                                <p>This is your personal product list. You can add, edit, delete your inhouse products.
                                    These prodcuts are shown in the website.</p>
                                <h3 class="mt-40">1. Product List</h3>
                                <p>Inhouse product list is shown here</p>
                                <h3 class="mt-40">2. Alert List</h3>
                                <p>Alert list is shown here</p>
                                <h3 class="mt-40">3. Out of stock List</h3>
                                <p>Out of stock list is shown here</p>
                                <h3 class="mt-40">4. Disabled Product List</h3>
                                <p>Disabled product list is shown here</p>
                                <h3 class="mt-40">5. Add New Product</h3>
                                <p>Admin can add new product in inhouse product list.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>


            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Inhouse Product (Create)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/products/inhouseproduct2.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Inhouse Product (Create)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/products/inhouseproduct3.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Save</h3>
                                <p>Save the product.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="RecentViewConfig">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Recent View Config</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Recent View Config</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/products/recentviewconfig.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Recent View Config</h3>
                                <p>The system store the recent view details for any user. You can customize the recent
                                    view settings from here. </p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Save the recently viewed product configuration.</p>

                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Review -->

            <!--Product Review -->
            <section class="infix-documentation mt-40" id="ProductReview">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Product Review</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Product Review</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/review/productreview.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Product Review</h3>
                                <p>User who buy the product can review it. After reviewing a product you can accept or
                                    declined it. </p>
                                <h3 class="mt-40">1. All Review</h3>
                                <p>All review list is shown here.</p>
                                <h3 class="mt-40">2. Pending </h3>
                                <p>Pending review list is shown here.</p>
                                <h3 class="mt-40">3. Declined</h3>
                                <p>Declined review list is shown here.</p>
                                <h3 class="mt-40">4. Action</h3>
                                <p>Review can be denied.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!--Seller Review -->
            <section class="infix-documentation mt-40" id="SellerReview">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Seller Review</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Seller Review</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/review/sellerreview.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Seller Review</h3>
                                <p>User can also review a seller. </p>
                                <h3 class="mt-40">1. All Review</h3>
                                <p>All review list is shown here.</p>
                                <h3 class="mt-40">2. Pending </h3>
                                <p>Pending review list is shown here.</p>
                                <h3 class="mt-40">3. Declined</h3>
                                <p>Declined review list is shown here.</p>
                                <h3 class="mt-40">4. Action</h3>
                                <p>Review can be denied.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="ReviewConfiguration">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Auto Approve Review Configuration</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Auto Approve Review Configuration</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/review/config.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Auto Approve Review Configuration</h3>
                                <p>Review will automatically approve if active otherwise admin has to manually approve
                                    the review. </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!--My Product Review -->
            <!-- <section class="infix-documentation mt-40" id="MyProductReview">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">My Product Review</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Product Review</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/review/myproductreview.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">My Product Review</h3>
                                <p>The reviews of your own product. You can also reply to the review.</p>
                                <h3 class="mt-40">1. Reply</h3>
                                <p>Reply to customer.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Product Review (Reply)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/review/myproductreview2.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section> -->

            <!--My Review -->
            <!-- <section class="infix-documentation mt-40" id="MyReview">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">My Review</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Review</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/review/myreview.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">My Review</h3>
                                <p> As you are also a seller , user can also review your store. You can find your review
                                    here. </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section> -->

            <!-- Order Manage -->

            <!--My Order -->
            <section class="infix-documentation mt-40" id="MyOrder">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">My Order</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Confirmed Order</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/order/myorder1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">My Order</h3>
                                <p>Here you can find the order of your product. In different tabs you can find different
                                    order list.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="MyOrder">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Completed Order</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/order/myorder2.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="MyOrder">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Pending Payment Order</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/order/myorder3.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="MyOrder">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Refused Order</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/order/myorder4.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!--Total Order -->
            <section class="infix-documentation mt-40" id="TotalOrder">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Total Order</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Total Order</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/order/totalorder.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Total Order</h3>
                                <p>You can find all order of all customer and seller. In different tabs you find
                                    different order list. </p>
                                <h3 class="mt-40">1. Pending Orders </h3>
                                <p>Pending orders list is shown here.</p>
                                <h3 class="mt-40">2. Confirmed Orders </h3>
                                <p>Confirmed orders list is shown here.</p>
                                <h3 class="mt-40">3. Completed Orders </h3>
                                <p>Completed orders list is shown here.</p>
                                <h3 class="mt-40">4. Pending Payment Orders </h3>
                                <p>Pending payment orders list is shown here.</p>
                                <h3 class="mt-40">5. Refused/Cancelled Orders </h3>
                                <p>Refused/Cancelled orders list is shown here.</p>
                                <h3 class="mt-40">6. Inhouse Orders </h3>
                                <p>Inhouse orders list is shown here.</p>
                                <h3 class="mt-40">7. Action </h3>
                                <p> Orders details can be shown here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Order Details</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/order/totalorder2.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Print</h3>
                                <p>Admin can print this page.</p>
                                <h3 class="mt-40">2. Update</h3>
                                <p>Admin can update the order information.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!--Inhouse Order -->
            <section class="infix-documentation mt-40" id="InhouseOrder">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Inhouse Order</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Inhouse Order</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/order/inhouse1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Inhouse Order</h3>
                                <p>You can find your inshouse product order list.</p>
                                <h3 class="mt-40">1. Confirmed Orders </h3>
                                <p>Confirmed orders list is shown here.</p>
                                <h3 class="mt-40">2. Completed Orders </h3>
                                <p>Completed orders list is shown here.</p>
                                <h3 class="mt-40">3. Pending Payment Orders </h3>
                                <p>Pending payment orders list is shown here.</p>
                                <h3 class="mt-40">4. Refused/Cancelled Orders </h3>
                                <p>Refused/Cancelled orders list is shown here.</p>
                                <h3 class="mt-40">6. Create New Order </h3>
                                <p>Admin can create new order from here.</p>
                                <h3 class="mt-40">6. Action </h3>
                                <p> Orders details can be shown here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>


            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Inhouse Order (Create)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/order/inhouse2.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!--Delivery Process -->
            <section class="infix-documentation mt-40" id="DeliveryProcess">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Delivery Process</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Delivery Process</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/order/deliveryprocess.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Delivery Process</h3>
                                <p>There are different delivery process in the system. You can add more process. When
                                    any user order a product he/she can see the product delivery status. Seller can
                                    update delivery status time to time. </p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Add new delivery process from here.</p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Admin can edit the delivery process details.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!--Cancel Reason -->
            <section class="infix-documentation mt-40" id="CancelReason">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Cancel Reason</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Cancel Reason</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/order/cancelreason.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Cancel Reason</h3>
                                <p>User can cancel any product. For cancelling any product he/she has to choose a
                                    reason. From here you can defined the reason.</p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Add new cancel reason from here.</p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Admin can edit and delete the reasons.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="TrackOrderConfig">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Track Order Configuration</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Track Order Configuration</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/order/config.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Track Order Configuration Website View</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <img src="img/order/config2.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Refund Manage -->
            <!--Pending Refund Requests -->
            <section class="infix-documentation mt-40" id="PendingRefundRequests">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Pending Refund Requests</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Pending Refund Requests</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/refund-manage/pendingrefund.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Pending Refund Requests</h3>
                                <p>User can request for refund a product. You can find the refund pending list here.</p>
                                <h3 class="mt-40">1. Action </h3>
                                <p>View refund details.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Refund Details</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/refund-manage/pendingrefund2.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Update</h3>
                                <p>Update refund statuses.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!--Confirmed Refund Requests -->
            <section class="infix-documentation mt-40" id="ConfirmedRefundRequests">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Confirmed Refund Requests</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Confirmed Refund Requests</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/refund-manage/confirmedrefund.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Confirmed Refund Requests</h3>
                                <p>When you update a pending refund request to confirmed , it will visiable here.</p>
                                <h3 class="mt-40">1. Action </h3>
                                <p>View refund details.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!--My Refund Requests -->
            <section class="infix-documentation mt-40" id="MyRefundRequests">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">My Refund Requests</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Refund Requests</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/refund-manage/myrefund.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">My Refund Requests</h3>
                                <p>As you are also a seller you will find your own product refund requests here.</p>
                                <h3 class="mt-40">1. Action </h3>
                                <p>View refund details.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Reasons -->
            <section class="infix-documentation mt-40" id="Reasons">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Refund Reasons</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Refund Reasons</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/refund-manage/reasons.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Refund Reasons</h3>
                                <p>To refund a product user has to choose a reason. You can manage the reasons here.</p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Add new reason from here.</p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Admin can edit and delete refund reasons.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Refund Process -->
            <section class="infix-documentation mt-40" id="RefundProcess">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Refund Process</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Refund Process</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/refund-manage/process.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Save </h3>
                                <p>Add new process from here.</p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Precess can edit and delete.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!--Refund Configuration -->
            <section class="infix-documentation mt-40" id="RefundConfiguration">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Refund Configuration</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Refund Configuration</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/refund-manage/configuration.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Refund Configuration</h3>
                                <p>You can configur refund setting here. You can enable and disable refund process and
                                    set the time for accepting refund request.</p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Save the configuration.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- System Settings -->
            <!-- General Settings  -->
            <section class="infix-documentation mt-40" id="GeneralSettings">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">General Settings </h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart General Settings </h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setting/general.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">General Setting</h3>
                                <p>You can setup many things from here.</p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Save the settings. </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Email Templete  -->
            <section class="infix-documentation mt-40" id="EmailTemplate">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Email Templete </h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Email Templete </h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setting/email1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Email Templete</h3>
                                <p>The system sends different type of email. You can setup email templete here. </p>
                                <h3 class="mt-40">1. Add New Templete </h3>
                                <p>Admin can add new email templete from here. </p>
                                <h3 class="mt-40">2. Activate </h3>
                                <p>Email templete can be activated and deactivated from here. </p>
                                <h3 class="mt-40">3. Manage </h3>
                                <p>Admin can manage and edit the templete. </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Email Templete (Create)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setting/email2.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Save</h3>
                                <p>Save the templete.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Company Information  -->
            <section class="infix-documentation mt-40" id="CompanyInformation">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Company Information </h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Company Information </h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setting/company.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Company Information</h3>
                                <p>As you are also a seller you can setup your company information. This information
                                    will seen by user as a seller. </p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Save the company information settings. </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- SMTP Settings  -->
            <section class="infix-documentation mt-40" id="SMTPSettings">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">SMTP Settings </h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart SMTP Settings </h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setting/smtp.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">SMTP Setting</h3>
                                <p>For sending email you need to setup smtp setting.</p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Save the smtp settings. </p>
                                <h3 class="mt-40">2. Send Test Mail </h3>
                                <p>After saving the setting test mail can be sent. </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- SMS Settings  -->
            <section class="infix-documentation mt-40" id="SMSSettings">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">SMS Settings </h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart SMS Settings </h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setting/sms.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">SMS Setting</h3>
                                <p>For sending sms you need to setup sms setting.</p>
                                <p>In url filed set api url which is get from api.</p>
                                <p>Then set SEND TO PARAMETER NAME, MESSAGE PARAMETER NAME, REQUEST METHOD according to api.</p>
                                <p>And set other parameter according to api.</p>

                                <h3 class="mt-40">1. Save </h3>
                                <p>Save the sms settings. </p>
                                <h3 class="mt-40">2. Send Test Sms </h3>
                                <p>After saving the setting sms can be sent. </p>

                                <h3>For Twillo:</h3>
                                <ul>
                                    <li>URL: https://api.twilio.com/2010-04-01/Accounts/{AccountSid}/Messages</li>
                                    <li>SEND TO PARAMETER NAME: To</li>
                                    <li>MESSAGE PARAMETER NAME: Body</li>
                                    <li>REQUEST METHOD: Select post method from dropdown.</li>
                                    <li>username: your AccountSid</li>
                                    <li>password: your api token</li>
                                    <li>From : your twillo number</li>
                                </ul>

                                <h3>For Nexmo:</h3>
                                <ul>
                                    <li>URL: https://rest.nexmo.com/sms/json</li>
                                    <li>from: Company Name</li>
                                    <li>SEND TO PARAMETER NAME: to</li>
                                    <li>MESSAGE PARAMETER NAME: text</li>
                                    <li>REQUEST METHOD: Select post method from dropdown.</li>
                                    <li>api_key: api key provied by nexmo</li>
                                    <li>api_secret: api secret provied by nexmo</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Analytic Tools  -->
            <section class="infix-documentation mt-40" id="AnalyticTools">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Analytic Tools </h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Analytic Tools </h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setting/analytic.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Analytic Tools</h3>
                                <p>You can setup different analytic tools for the website.</p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Save the analytic tools settings. </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Activation  -->
            <section class="infix-documentation mt-40" id="Activation">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Activation </h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Activation </h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setting/activation.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Activation</h3>
                                <p>You can activate and deactive different functionalities from here.</p>
                                <h3 class="mt-40">1. Activate </h3>
                                <p>Admin can activate and deactivate many settings. </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="NotificationSetting">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Notification Setting </h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Notification Setting </h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setting/notification1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Notification Setting</h3>
                                <p>You can defined how the notification will send from the system.</p>
                                <h3 class="mt-40">1. Edit </h3>
                                <p>You can edit notification message and type. </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Notification Setting Edit</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setting/notification2.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="SocialLoginConfig">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Social Login Configuration</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Social Login Configuration</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setting/social.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Social Login Configuration</h3>
                                <p>User can login to the system using social site. You have to configure the api key
                                    from here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="MaintenanceMode">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Maintenance Mode </h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Maintenance Mode </h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setting/maintanence.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Maintenance Mode</h3>
                                <p>In maintenance mode users cannot access to the system. You can go the maintenance
                                    mode according to your necessary</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="AboutUpdate">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">About & Update </h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart About & Update </h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setting/about_update.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">About & Update</h3>
                                <p>You can update the system from here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="ModuleManage">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Module Manage </h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Module Manage </h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setting/module_manage.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Module Manage</h3>
                                <p>You can add, buy, update different types of module. </p>
                                <h3 class="mt-40">1. Add </h3>
                                <p>Add or update module. </p>
                                <h3 class="mt-40">2. Buy now </h3>
                                <p>Buy new module. </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Payment Gateways -->
            <section class="infix-documentation mt-40" id="PaymentGateways">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Payment Gateways</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Payment Gateways</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/payment-gateway/1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Payment Gateways</h3>
                                <p>User can pay using different payment gateways. You can setup payment gateways here.
                                </p>
                                <h3 class="mt-40">1. Activation </h3>
                                <p>Admin can change the activation. If any gateway is active it will show in the side
                                    panel where payment gateways information can be updated. It will also show in the
                                    website for the users to pay through this gateway. </p>
                                <h3 class="mt-40">2. Update </h3>
                                <p>Give necessary details of payment method and update it. </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Payment Gateways Website View</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <img src="img/payment-gateway/2.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>


            <!-- Setup -->
            <!-- Language Settings -->
            <section class="infix-documentation mt-40" id="LanguageSettings">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Language Settings</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Language Settings</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setup/language1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Language Setting</h3>
                                <p>Setup the languages for the website. User can change the lauguage.</p>
                                <h3 class="mt-40">1. Add New Language </h3>
                                <p>Admin can create new language.</p>
                                <h3 class="mt-40">2. Activate </h3>
                                <p>Admin can activate and deactivate any language.</p>
                                <h3 class="mt-40">3. Action</h3>
                                <p>Admin can edit, delete any language. Language translation can be added from here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Language (Create)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setup/language2.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Save</h3>
                                <p>Save the language.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Language (Translation)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setup/language3.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Save</h3>
                                <p>Save the language translation.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Currency -->
            <section class="infix-documentation mt-40" id="Currency">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Currency</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Currency</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setup/currency.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Currency</h3>
                                <p>You can setup the currency for the website. User can change currency.</p>
                                <h3 class="mt-40">1. Add New Currency </h3>
                                <p>Admin can create new currency for the system.</p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Currency can be edited from here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Currency (Create)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setup/currency2.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Save</h3>
                                <p>Save the currency.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- tag -->
            <section class="infix-documentation mt-40" id="Tag">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Tag</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Tag</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setup/tag.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Tag</h3>
                                <p>Tag is import for search any product or blog or gift card.</p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Add new tag.</p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Edit and delete the tags.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Location -->
            <section class="infix-documentation mt-40" id="Location">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Location</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Location(Country)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setup/location1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Location</h3>
                                <p>You can setup the locations. When user add a delivery address he/she can choose the
                                    location. </p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Add new country.</p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Edit the country information.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Location(State)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setup/location2.png"
                                    class="img img-responsive general-setting documentationImg">


                                <h3 class="mt-40">1. Save </h3>
                                <p>Add new state.</p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Edit the state information.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Location(City)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setup/location3.png"
                                    class="img img-responsive general-setting documentationImg">

                                <h3 class="mt-40">1. Save </h3>
                                <p>Add new city.</p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Edit the city information.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>


            <!-- <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Dashboard View</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setup/dashboard2.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section> -->
            <!-- Shipping Method -->
            <section class="infix-documentation mt-40" id="ShippingMethod">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Shipping Method</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Shipping Method</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/setup/shipping.png"
                                    class="img img-responsive general-setting documentationImg">

                                <h3 class="mt-40">Shipping Method</h3>
                                <p>You can manage shipping methods. User can choose shipping method for delivery.</p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Add new shipping method.</p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Edit and delete the shipping method information.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Gst Setup -->

            <!-- Gst List -->
            <section class="infix-documentation mt-40" id="GSTList">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Gst List</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Gst List</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/gstsetup/gst.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">GST List</h3>
                                <p>You can manage different type of gst here. </p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Add new gst.</p>
                                <h3 class="mt-40">2. Action </h3>
                                <p>Admin can edit and delete GST.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Configuration -->
            <section class="infix-documentation mt-40" id="GstConfiguration">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">GST Configuration</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart GST Configuration</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/gstsetup/configuration.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">GST Configuration </h3>
                                <p>You can configure the gst from here. </p>
                                <h3 class="mt-40">1. Update </h3>
                                <p>Update the GST configuration values.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>


            <!-- Account -->

            <!-- Chart Of Account -->
            <section class="infix-documentation mt-40" id="ChartOfAccounts">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Chart Of Account</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Chart Of Account</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/account/chartofaccount1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Chart Of Account</h3>
                                <p>You can manage differeny types of account here.</p>
                                <h3 class="mt-40">1. Chart of Account </h3>
                                <p>Create new chart of account.</p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Admin can edit and delete chart of account.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Chart Of Account (Create)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/account/chartofaccount2.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Submit</h3>
                                <p>Save the new account.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Bank Accounts -->
            <section class="infix-documentation mt-40" id="BankAccounts">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Bank Accounts</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Bank Accounts</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/account/backaccount1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Bank Accounts</h3>
                                <p>For receiving payment you might be need bank accounts. You can manage bank accounts
                                    here.</p>
                                <h3 class="mt-40">1. New Account </h3>
                                <p>Create new account.</p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Admin can edit and delete account. Account history can also be seen from here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Bank Account (Create)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/account/backaccount2.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Submit</h3>
                                <p>Save the new account.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Bank Account (Details)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/account/backaccount3.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Income -->
            <section class="infix-documentation mt-40" id="Income">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Income</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Income</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/account/income.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Income</h3>
                                <p>Calculate your income here.</p>
                                <h3 class="mt-40">1. New Income </h3>
                                <p>Create new income.</p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Admin can edit and delete income.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Expenses -->
            <section class="infix-documentation mt-40" id="Expenses">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Expenses</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Expenses</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/account/expense.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Expenses</h3>
                                <p>Calculate your expenses here.</p>
                                <h3 class="mt-40">1. New Expenses </h3>
                                <p>Create new expense.</p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Admin can edit and delete expense.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Profit -->
            <section class="infix-documentation mt-40" id="Profit">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Profit</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Profit</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/account/profit.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Profit</h3>
                                <p>When you add your income and expenses the system will automaticall calculate your
                                    profit. You can find the details here.</p>
                                <h3 class="mt-40">1. Search </h3>
                                <p>Search profit according to date range.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Transaction -->
            <section class="infix-documentation mt-40" id="Transaction">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Transaction</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Transaction</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/account/transaction.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Transaction</h3>
                                <p>When any user use payment gateway to pay , it creates a transaction history. You will
                                    find all the transaction history here.</p>
                                <h3 class="mt-40">1. Select Criteria </h3>
                                <p>Transaction details can be found according to date range.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Statement -->
            <section class="infix-documentation mt-40" id="Statement">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Statement</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Statement</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/account/statement.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Statement</h3>
                                <p>Manage different types of statements. </p>
                                <h3 class="mt-40">1, 2 & 3. Select Criteria </h3>
                                <p>Admin can choose different criteria.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>


            <!-- Support Ticket -->

            <!-- Support Ticket -->
            <section class="infix-documentation mt-40" id="SupportTicket">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Support Ticket</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Support Ticket</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/supportticket/suportticket1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Support Ticket</h3>
                                <p>For different types of support , user can create support ticket. You can manage
                                    support ticket here.</p>
                                <h3 class="mt-40">1. Add New</h3>
                                <p>Add new support ticket.</p>
                                <h3 class="mt-40">2. Search </h3>
                                <p>Search support ticket according to category, priority and status.</p>
                                <h3 class="mt-40">3. Agent Asign </h3>
                                <p>Admin can change who will be assign to the ticket.</p>
                                <h3 class="mt-40">4. Action </h3>
                                <p>Admin can show, edit and delete support ticket.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Support Ticket (Create)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/supportticket/suportticket2.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Create Ticket</h3>
                                <p>Create the support ticked by giving the necessary information.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Category -->
            <section class="infix-documentation mt-40" id="SupportTicketCategory">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Support Ticket Category</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Support Ticket Category</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/supportticket/category.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Support Ticket Category</h3>
                                <p>For creating a support ticket you need to choose a category. You can manage the
                                    category here.</p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Create new category.</p>
                                <h3 class="mt-40">2. Action </h3>
                                <p>Admin can edit and delete any category.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Priority -->
            <section class="infix-documentation mt-40" id="Priority">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Priority</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Priority</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/supportticket/priority.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Priority</h3>
                                <p>Support ticket has different priority. You can manage support ticket priority here.
                                </p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Create new priority.</p>
                                <h3 class="mt-40">2. Action </h3>
                                <p>Admin can edit and delete any priority.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Status -->
            <section class="infix-documentation mt-40" id="Status">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Status</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Status</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/supportticket/status.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Status</h3>
                                <p>Support ticket has different status. You can manage support ticket status here.</p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Create new status.</p>
                                <h3 class="mt-40">2. Action </h3>
                                <p>Admin can edit and delete any status.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- My Ticket -->
            <!-- <section class="infix-documentation mt-40" id="MyTicket">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">My Ticket</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Ticket</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/supportticket/myticket.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">My Ticket</h3>
                                <p>You will find your own support ticket which you created.</p>
                                <h3 class="mt-40">1. Search </h3>
                                <p>Search your support ticket according to category, priority and status.</p>
                                <h3 class="mt-40">2. Action </h3>
                                <p>View your own support ticket.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section> -->

            <!-- All Activity Tools -->
            <!-- Activity Logs -->
            <section class="infix-documentation mt-40" id="ActivityLogs">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Activity Logs</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Activity Logs</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/allactivity/activitylogs.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Activity Logs</h3>
                                <p>All the activities of the system is stored. You will find it here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Login Activity -->
            <section class="infix-documentation mt-40" id="LoginActivity">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Login Activity</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Login Activity</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/allactivity/login.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Login Activity</h3>
                                <p>System stores all the login and logout information of the users. You can find it
                                    here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>


            <!-- Human Resource -->
            <!-- Staff -->
            <section class="infix-documentation mt-40" id="Staff">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Staff</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Staff</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/humanresource/staff1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Staff</h3>
                                <p>Staffs are like your company employee. You can use them to do specific task. </p>
                                <h3 class="mt-40">1. Add New Staff </h3>
                                <p>Admin can create new staff.</p>
                                <h3 class="mt-40">2. Action </h3>
                                <p>Admin can view, edit and delete staff.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Staff (Create)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/humanresource/staff2.png"
                                    class="img img-responsive general-setting documentationImg">

                                <h3 class="mt-40">1. Save</h3>
                                <p>Save the staff.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Role -->
            <section class="infix-documentation mt-40" id="Role">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Role</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Role</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/humanresource/role1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Role</h3>
                                <p>Every staff and user has a specific role. Role means what a user can do. Different
                                    types of permissions are given to a specific role. </p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Create new role.</p>
                                <h3 class="mt-40">2. Assign Permission </h3>
                                <p>Admin can assign permissions to any role.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Role Permission</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/humanresource/role2.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Submit</h3>
                                <p>Assign the specific permissions to the role.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Department -->
            <section class="infix-documentation mt-40" id="Department">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Department</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Department</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/humanresource/department1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Department</h3>
                                <p>You can manage your company department. </p>
                                <h3 class="mt-40">1. Add New Department </h3>
                                <p>Admin can create new department.</p>
                                <h3 class="mt-40">2. Action </h3>
                                <p>Admin can edit and delete department.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Department (Create)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/humanresource/department2.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Save</h3>
                                <p>Save the department.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Attendance -->
            <section class="infix-documentation mt-40" id="Attendance">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Attendance</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Attendance</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/humanresource/attendance.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Attendance</h3>
                                <p>You can take your staff attendance here.</p>
                                <h3 class="mt-40">1 & 2. Select Role and Date </h3>
                                <p>Select role and date to give attendance.</p>
                                <h3 class="mt-40">3. Save</h3>
                                <p>Save the attendance.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Attendance Report -->
            <section class="infix-documentation mt-40" id="AttendanceReport">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Attendance Report</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Attendance Report</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/humanresource/attendancereport.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Attendance Report</h3>
                                <p>You can generate your staff attendance report here. </p>
                                <h3 class="mt-40">1. Search </h3>
                                <p>Select role, month and year for attendance report generate.</p>
                                <h3 class="mt-40">2. Attendance Report Print </h3>
                                <p>You can print the attendance report.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Event -->
            <section class="infix-documentation mt-40" id="Event">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Event</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Event</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/humanresource/event.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Event</h3>
                                <p>You can manage event here.</p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Admin can create new event.</p>
                                <h3 class="mt-40">2. Action </h3>
                                <p>Admin can edit and delete event.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Holiday Setup -->
            <section class="infix-documentation mt-40" id="HolidaySetup">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Holiday Setup</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Holiday Setup</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/humanresource/holiday.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Holiday Setup</h3>
                                <p>Setup holiday for staffs.</p>
                                <h3 class="mt-40">1. Copy Previous Year Setting </h3>
                                <p>Save the previous year setting for the year.</p>
                                <h3 class="mt-40">Submit</h3>
                                <p>Admin can add new holiday.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>




            <!-- Visitor Setup -->
            <!-- Ignore IP -->
            <section class="infix-documentation mt-40" id="IgnoreIP">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Ignore IP</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Ignore IP</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/visitor/1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Ignore Ip</h3>
                                <p>If you want to prevent a user to use the application you can do it by giving the IP
                                    address in ignore IP. </p>
                                <h3 class="mt-40">1. Save</h3>
                                <p>Ignore IP cannot enter the website. You can add new ip address from here.</p>
                                <h3 class="mt-40">2. Action</h3>
                                <p>Delete any ignored IP.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="SidebarManage">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Sidebar Manage</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Sidebar Manage</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/sidebar.png" class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Sidebar Manage</h3>
                                <p>Admin can rearrenge the sidebar from here by drag and drop. </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>


            <!-- Admin Report -->
            <!-- Seller Wise Sales -->
            <section class="infix-documentation mt-40" id="SellerWiseSales">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Seller Wise Sales</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Seller Wise Sales</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/adminreport/seller_wise_sale.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Keyword Searches -->
            <section class="infix-documentation mt-40" id="KeywordSearches">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Keyword Searches</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Keyword Searches</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/adminreport/keyword.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Keyword Searches</h3>
                                <p>You can find the report of which keywords are searched in the application.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Visitor -->
            <section class="infix-documentation mt-40" id="Visitor">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Visitor</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Visitor</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/adminreport/visitor.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Visitor</h3>
                                <p>If anyone visit the website, the system store the ip address of the user. You can
                                    find the visitor list here.</p>

                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="InhouseProductSale">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Inhouse Product Sale</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Inhouse Product Sale</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/adminreport/inhouse.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Inhouse Product Sale</h3>
                                <p>You can find the inhouse product sale report here. You can filter by sale type.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="ProductStock">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Product Stock</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Product Stock</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/adminreport/product_stock.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Product Stock</h3>
                                <p>Product stock list is here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="Wishlist">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Wishlist</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Wishlist</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/adminreport/wishlist.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Wishlist</h3>
                                <p>If any user add any product to his/her wishlist, you can find it here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="WalletRechargeHistory">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Wallet Recharge History</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Wallet Recharge History</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/adminreport/wallet_recherge.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Wallet Recharge History</h3>
                                <p>You can find the wallet recharge history report here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="TopSeller">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Top Seller</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Top Seller</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/adminreport/top_customer.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Top Seller</h3>
                                <p>You can find the report of top seller who sales the most.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="TopCustomer">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Top Customer</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Top Customer</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/adminreport/top_customer.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Top Customer</h3>
                                <p>You can find the top customer who spent the most.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="TopSellingItem">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Top Selling Item</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Top Selling Item</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/adminreport/top_selling_item.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Top Selling Item</h3>
                                <p>You can find the report of product which has sold the most.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="Order">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Order</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Order</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/adminreport/order.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Order</h3>
                                <p>You can find the order report here. You can filter the report according to your
                                    necessary.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="Payment">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Payment</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Payment</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/adminreport/payment.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Payment</h3>
                                <p>Payment history report is here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="AdminProductReview">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Product Review</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Product Review</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/adminreport/product_review.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Product Review</h3>
                                <p>You can find the product review report here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="AdminSellerReview">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Seller Review</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Seller Review</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/adminreport/seller_review.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Seller Review</h3>
                                <p>You can find the seller review report here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Customer Panel -->
            <!-- My Purchases -->
            <section class="infix-documentation mt-40" id="MyPurchases">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">My Purchases</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Purchases</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerpanel/mypurchase.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">My Purchase</h3>
                                <p>Whatever you purchase from the website you can find it here. You can also see
                                    different types of details of your orders. </p>
                                <h3 class="mt-40">1. Purchase List </h3>
                                <p>View the purchase products.</p>
                                <h3 class="mt-40">2. To Pay</h3>
                                <p>View the paying product list.</p>
                                <h3 class="mt-40">3. To Ship</h3>
                                <p>View the shipping product list.</p>
                                <h3 class="mt-40">4. To Recieve</h3>
                                <p>View the reveiving product list.</p>
                                <h3 class="mt-40">5. Download Invoice</h3>
                                <p>Download the invoice.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Gift Card -->
            <section class="infix-documentation mt-40" id="GiftCard">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Gift Card</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Gift Card</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerpanel/giftcard.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Gift Card</h3>
                                <p>If you buy any gift card, you can find it here. You can use the gift card to recharge
                                    your wallet.</p>
                                <h3 class="mt-40">1. View </h3>
                                <p>View the secret code.</p>
                                <h3 class="mt-40">2. Redeem </h3>
                                <p>By clicking the redeem button you can add the balance to your wallet.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Digital Product -->
            <section class="infix-documentation mt-40" id="DigitalProduct">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Digital Product</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Digital Product</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerpanel/digital_purchased.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Digital Product</h3>
                                <p>If you buy any digital product , you can find it here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- My Wishlist -->
            <section class="infix-documentation mt-40" id="MyWishlist">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">My Wishlist</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Wishlist</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerpanel/mywishlist.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Wishlist</h3>
                                <p>If you add any product to your wishlist you can find it here. /p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Refund Dispute -->
            <section class="infix-documentation mt-40" id="RefundDispute">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Refund & Dispute</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Refund & Dispute</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerpanel/refund.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Refund & Dispute </h3>
                                <p>When you request any product for refund You can see it here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- My Coupon -->
            <section class="infix-documentation mt-40" id="MyCoupon">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">My Coupon</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Coupon</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerpanel/coupon.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">My Coupon</h3>
                                <p>If you want to save any coupon for later use you can save it here.</p>
                                <h3 class="mt-40">1. Add </h3>
                                <p>Add the coupon for later use.</p>
                                <h3 class="mt-40">2. Delete </h3>
                                <p>Delete the coupon.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- My Account -->
            <section class="infix-documentation mt-40" id="MyAccount">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">My Account</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Account</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerpanel/profile.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">My Account</h3>
                                <p>You can find your personal information here.</p>
                                <h3 class="mt-40">2. Update </h3>
                                <p>Update basic information.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Account(Change Password)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerpanel/profile2.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">4. Update</h3>
                                <p>Update the old password.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Account(Address)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerpanel/profile3.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">6. Add New Address</h3>
                                <p>You can add new address to your account.</p>
                                <h3 class="mt-40">7. Edit</h3>
                                <p>Address can be edited.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Account</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerpanel/profile4.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">8. Save</h3>
                                <p>Save the new address.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="MyReferral">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">My Referral</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Referral</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerpanel/my_referral.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40"> My Referral</h3>
                                <p>Using referral code user can get discount. You can get your referral code from here
                                    and see the users who used your referral code.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="Backup">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Backup</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Backup</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/backup.png" class="img img-responsive general-setting documentationImg">

                                <h3 class="mt-40"> Backup</h3>
                                <p>Backup your system.</p>
                                <h3 class="mt-40"> 1. Generate New Backup</h3>
                                <p>You can generate new backup.</p>
                                <h3 class="mt-40"> 2. Delete</h3>
                                <p>Delete old backup.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="Utilities">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Utilities</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Utilities</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/utilities.png"
                                    class="img img-responsive general-setting documentationImg">

                                <h3 class="mt-40"> Utilities</h3>
                                <p>Admin have different utilities in the system.Admin can perform different utility
                                    action from here.</p>
                                <h3 class="mt-40"> 1. Clear cache</h3>
                                <p>Clear your application cache data.</p>
                                <h3 class="mt-40"> 2. Clear Log</h3>
                                <p>For any action the system generate a log. You can create it from here.</p>
                                <h3 class="mt-40"> 3. Disable App debug</h3>
                                <p>If you disable app debug system will show the error exceptions.</p>
                                <h3 class="mt-40"> 4. Enable force https</h3>
                                <p>Https is a secure connection. You can enable https from here.</p>
                                <h3 class="mt-40"> 5. Reset Database</h3>
                                <p>Be careful to reset your database, it will erase all your data.</p>
                                <h3 class="mt-40"> 6. XML Sitemap</h3>
                                <p>You can generate xml sitemap from here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>




            <!--            End Parent Panel-->
        </div>
    </div>

    <!-- ================Footer Area ================= -->
    <footer class="footer-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12"></div>
            </div>
        </div>
    </footer>
    <!-- ================End Footer Area ================= -->

    <!-- Optional JavaScript -->
    <!-- jQuery first, then Popper.js, then Bootstrap JS -->
    <script src="vendors/js/jquery-3.2.1.min.js">
    </script>
    <script src="vendors/js/jquery-ui.js">
    </script>
    <script src="vendors/js/jquery.data-tables.js">
    </script>
    <script src="vendors/js/dataTables.buttons.min.js">
    </script>
    <script src="vendors/js/buttons.flash.min.js">
    </script>
    <script src="vendors/js/jszip.min.js">
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/pdfmake.min.js">
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/vfs_fonts.js">
    </script>
    <script src="vendors/js/buttons.html5.min.js">
    </script>
    <script src="vendors/js/buttons.print.min.js">
    </script>
    <script src="vendors/js/dataTables.rowReorder.min.js">
    </script>
    <script src="vendors/js/dataTables.responsive.min.js">
    </script>
    <script src="vendors/js/buttons.colVis.min.js">
    </script>
    <script src="vendors/js/popper.js">
    </script>
    <script src="vendors/js/bootstrap.min.js">
    </script>
    <script src="vendors/js/nice-select.min.js">
    </script>
    <script src="vendors/js/jquery.magnific-popup.min.js">
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2. 18.1/moment.js" type="text/javascript"></script>
    <script
        src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4. 17.47/js/bootstrap-datetimepicker.min.js"></script>
    <script src="vendors/js/bootstrap-datepicker.min.js">
    </script>
    <script src="js/main.js">
    </script>
    <script type="text/javascript">
        $(document).ready(function () {
            var sections = [];
            var scrolled_id = false;
            var id = false;
            var $navbar = $('#sidebar');
            var $navbar__links = $navbar.find('.spy_scroll');

            $navbar__links.each(function () {
                sections.push($($(this).attr('href')));
            });

            $('.spy_scroll').bind('click', function (event) {
                var $anchor = $(this);
                var headerH = '0';
                $('html, body').stop().animate({
                    scrollTop: $($anchor.attr('href')).offset().top - headerH + "px"
                }, 5000, 'easeInOutExpo');
                event.preventDefault();
            });

            $(window).scroll(function (e) {
                e.preventDefault();
                var scrollTop = $(this).scrollTop() + ($(window).height() / 3);


                for (var i in sections) {
                    var section = sections[i];

                    if (scrollTop > section.offset().top) {
                        scrolled_id = section.attr('id');
                    }

                    if (scrolled_id !== id) {
                        id = scrolled_id;

                        $navbar__links.removeClass('spy_scroll--current');

                        $('a[href="#' + id + '"]', $navbar).addClass('spy_scroll--current');
                        $('a[href="#' + id + '"]', $navbar).closest('.list-unstyled').addClass('show');
                        $('a[href="#' + id + '"]', $navbar).closest('.list-unstyled').siblings('.dropdown-toggle').addClass('active');
                        // $('a[href="#' + id + '"]', $navbar).closest('.list-unstyled').addClass('active'); 


                    }
                }
            });

            $(window).trigger('scroll');
        });

    </script>
</body>

</html>