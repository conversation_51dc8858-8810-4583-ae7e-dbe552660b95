<?php
return [
    'Select your Expense Account' => 'Selezioni il vostro conto di spesa',
    'Chart Of Accounts' => ' Piano contabile',
    'New Account' => ' Nuovo conto',
    'Add As A Parent Account' => ' Aggiunga come conto del genitore',
    'Parent account selection will add your account as a sub account' => ' La selezione di conto del genitore aggiungerà il vostro conto come sotto conto',
    'Select your Income Account' => ' Selezioni il vostro conto proventi',
    'Account code need to be unique, Leave blank for auto generate an unique account code' => "Necessità di codice di conto di essere unico, spazio in bianco di permesso per l'auto generare un codice di conto unico",
    'Selecting a default Account, will remove previously default account for selected item' => "La selezione del conto di difetto, rimuoverà precedentemente il difetto rappresenta l'oggetto selezionato",
    'Select Default Account For' => ' Il difetto scelto rappresenta',
    'You can\'t delete an account which has child element' => ' Potete \ “t cancellare un conto che ha elemento del bambino',
    'The requested chart of account is not found' => ' Il grafico del conto richiesto non è trovato',
    'The requested chart of account deleted successful' => ' Il grafico del conto richiesto ha cancellato riuscito',
    'The requested chart of account created successful' => ' Il grafico del conto richiesto ha creato riuscito',
    'Income Account' => ' Conto proventi',
    'Expense Account' => ' Conto di spesa',
    'Select Account' => ' Conto scelto',
    'Payment Method' => ' Metodo di pagamento',
    'Select your Payment method' => ' Selezioni il vostro metodo di pagamento',
    'Edit Account' => 'Modifica account',
    'The requested chart of account updated successful' => 'Il piano dei conti richiesto è stato aggiornato correttamente',
];
