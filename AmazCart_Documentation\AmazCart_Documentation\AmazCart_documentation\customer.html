<!DOCTYPE html>
<html lang="">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <link rel="icon" href="img/favicon.png" type="image/png" />
    <title>Amazcart</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="vendors/css/jquery-ui.css" />
    <link rel="stylesheet" href="vendors/css/bootstrap.css" />
    <link rel="stylesheet" href="vendors/css/jquery.data-tables.css">
    <link rel="stylesheet" href="vendors/css/buttons.dataTables.min.css">
    <link rel="stylesheet" href="vendors/css/rowReorder.dataTables.min.css">
    <link rel="stylesheet" href="vendors/css/responsive.dataTables.min.css">
    <link rel="stylesheet" href="vendors/css/bootstrap-datepicker.min.css" />
    <link rel="stylesheet" href="vendors/css/bootstrap-datetimepicker.min.css" />
    <link rel="stylesheet" href="vendors/css/themify-icons.css" />
    <link rel="stylesheet" href="vendors/css/flaticon.css" />
    <link rel="stylesheet" href="vendors/css/font-awesome.min.css" />
    <link rel="stylesheet" href="vendors/css/magnific-popup.css" />
    <link rel="stylesheet" href="vendors/css/nice-select.css" />
    <!-- main css -->
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="css/documentation.css" />
</head>


<body class="version1" data-spy="scroll" data-target="#sidebar" data-offset="50">
    <div class="main-wrapper">
        <!-- Sidebar  -->
        <nav id="sidebar" class="active">
            <div class="sidebar-header">
                <a href="index.html">
                    <!-- <img src="img/logo.png" alt=""> -->
                </a>
            </div>

            <ul class="list-unstyled components">

                <li>
                    <a href="#initiate" class="active">
                        <span class="fa fa-angle-double-right"></span>
                        initiate
                    </a>
                </li>

                <li>
                    <a href="#subMenuCustomerPanelSetting" data-toggle="collapse" aria-expanded="false"
                        class="dropdown-toggle">
                        <span class="flaticon-cabinet"></span>
                        Amazcart
                    </a>
                    <ul class="collapse list-unstyled" id="subMenuCustomerPanelSetting">
                        <li><a class="spy_scroll" href="#Dashboard"> Dashboard</a></li>
                        <li><a class="spy_scroll" href="#MyPurchases">My Order</a></li>
                        <li><a class="spy_scroll" href="#GiftCard">Gift Card</a></li>
                        <!-- <li><a class="spy_scroll" href="#DigitalProduct">Digital Product</a></li> -->
                        <li><a class="spy_scroll" href="#Notification">Notification</a></li>
                        <li><a class="spy_scroll" href="#MyWishlist">My Wishlist</a></li>
                        <li><a class="spy_scroll" href="#RefundDispute">Refund Dispute</a></li>
                        <li><a class="spy_scroll" href="#MyCoupon">My Coupon</a></li>
                        <li><a class="spy_scroll" href="#MyAccount">My Account</a></li>
                        <li><a class="spy_scroll" href="#MyWallet">My Wallet</a></li>
                        <li><a class="spy_scroll" href="#Referral"> Referral</a></li>
                        <li><a class="spy_scroll" href="#SupportTicket">Support Ticket</a></li>
                    </ul>
                </li>

            </ul>
        </nav>


        <!-- Page Content  -->
        <div id="main-content">
            <nav class="navbar navbar-expand-lg">

            </nav>


            <section class="mb-40" id="initiate">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row border-bottom text-center">
                            <div class="col-lg-12">
                                <h1>Welcome</h1>
                                <h3>To</h3>
                                <h1 class="mb-20">Amazcart ultimate Ecommerce</h1>

                                <h3>Ultimate solution for your ecommerce business</h3>
                                <h1>By</h1>
                                <h1> <a href="https://spondonit.com/" target="blank"> SPONDONIT</a></h1>
                            </div>
                        </div>
                        <div class="row mt-40 border-bottom">
                            <div class="col-lg-12">
                                <p><strong>Email:</strong><a
                                        href="mailto:<EMAIL>"><EMAIL></a><br>
                                    <strong>Web:</strong><a href="http://infixedu.com" target="blank"> infixedu.com</a>
                                </p>
                            </div>
                        </div>
                        <div class="row mt-40">
                            <div class="col-lg-12">
                                <p>We would like to thank you for purchasing Amazcart Ecommerce System! We are very
                                    pleased you
                                    have chosen Amazcart Ecommerce System for your business, you will not be
                                    disappointed!
                                    Before you get started, please be sure to always check out these documentation
                                    files. We outline all kinds of good information, and provide you with all the
                                    details you need to use Amazcart Ecommerce System. </p>

                                <p>If you are unable to find your answer here in our documentation, watch our Video
                                    Tutorials, you can also visit our Help & Support. Chances are your question or issue
                                    have been brought up already and the answer is waiting to be found. If you are
                                    unable to find it anywhere, then please go our Support section and open a new
                                    Support Ticket with all the details we need. Please be sure to include your site URL
                                    as well. Thank you, we hope you enjoy using Amazcart Ecommerce System!</p>
                            </div>


                            <div class="col-md-12 docs-initiate text-center mt-40">

                                <a class="primary-btn small fix-gr-bg"
                                    href="https://www.youtube.com/watch?v=DhZ6p_tYnpk&list=PLiYjRtR_wL1UPUXpYHjPZGI9qQ-awTQat"
                                    target="blank">Video Tutorials</a>
                                <a class="primary-btn small tr-bg" href="https://ticket.spondonit.com/help-center"
                                    target="blank">Help & Supports</a>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="GetSupport">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Get Support</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Get Support from Amazcart</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <p>To get Technical/Sales support. You must create Amazcart Account first.</p>
                                <div class="row">
                                    <div class="col-sm-8">
                                        <h3>Contact Info</h3>
                                        <p style="margin: 0px !important;"> <b>Email:</b> <a
                                                href="mailto:<EMAIL>"> <EMAIL></a></p>
                                        <p style="margin: 0px !important;"> <b>Web:</b> <a
                                                href="http://infixedu.com">infixedu.com</a></p>
                                        <p style="margin: 0px !important;"> <b>Support:</b> <a
                                                href="http://ticket.codepixar.com/">http://ticket.codepixar.com</a></p>


                                    </div>
                                    <div class="col-sm-4">
                                        <img src="img/support.png" class="img img-responsive" style="width: 100%">
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>



            <!-- Customer Panel -->
            <!-- Dashboard -->
            <section class="infix-documentation mt-40" id="Dashboard">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Dashboard</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Dashboard</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerdashboard/dashboard.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Dashboard </h3>
                                <p>This is the dashboard panel for customer. Different statistic are here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- My Order -->
            <section class="infix-documentation mt-40" id="MyPurchases">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">My Order</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Order</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerdashboard/order.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">My Order</h3>
                                <p>Whatever you purchase from the website you can find it here. You can also see
                                    different types of details of your orders. </p>
                                <h3 class="mt-40">1. All </h3>
                                <p>View the purchase products.</p>
                                <h3 class="mt-40">2. To Pay</h3>
                                <p>View the paying product list.</p>
                                <h3 class="mt-40">3. To Ship</h3>
                                <p>View the shipping product list.</p>
                                <h3 class="mt-40">4. To Recieve</h3>
                                <p>View the reveiving product list.</p>
                                <h3 class="mt-40">5. Download Invoice</h3>
                                <p>Download the invoice.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Gift Card -->
            <section class="infix-documentation mt-40" id="GiftCard">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Gift Card</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Gift Card</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerdashboard/giftcard.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Gift Card</h3>
                                <p>If you buy any gift card, you can find it here. You can use the gift card to recharge
                                    your wallet.</p>
                                <h3 class="mt-40">1. Redeem </h3>
                                <p>By clicking the redeem button you can add the balance to your wallet.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Digital Product -->
            <!-- <section class="infix-documentation mt-40" id="DigitalProduct">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Digital Product</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Digital Product</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerdashboard/order/sdf"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Digital Product</h3>
                                <p>If you buy any digital product , you can find it here.</p>
                                <h3 class="mt-40">1. Save </h3>
                                <p>Save the setting.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section> -->
            <section class="infix-documentation mt-40" id="Notification">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Notification</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Notification</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerdashboard/notification.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">Notification</h3>
                                <p>You can see all the notifications here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- My Wishlist -->
            <section class="infix-documentation mt-40" id="MyWishlist">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">My Wishlist</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Wishlist</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerdashboard/wishlist.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">My Wishlist</h3>
                                <p>If you add any product to your wishlist you can find it here. /p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Refund Dispute -->
            <section class="infix-documentation mt-40" id="RefundDispute">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Refund & Dispute</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Refund & Dispute</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerdashboard/refund.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- My Coupon -->
            <section class="infix-documentation mt-40" id="MyCoupon">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">My Coupon</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Coupon</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerdashboard/coupon.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">My Coupon</h3>
                                <p>If you want to save any coupon for later use you can save it here.</p>
                                <h3 class="mt-40">1. Add </h3>
                                <p>Add the coupon for later use.</p>
                                <h3 class="mt-40">2. Delete </h3>
                                <p>Delete the coupon.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- My Account -->
            <section class="infix-documentation mt-40" id="MyAccount">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">My Account</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Account</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerdashboard/myaccount1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">My Account</h3>
                                <p>You can find your personal information here.</p>
                                <h3 class="mt-40">2. Update </h3>
                                <p>Update basic information.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Account(Change Password)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerdashboard/myaccount2.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">4. Update</h3>
                                <p>Update the old password.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Account(Address)</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerdashboard/myaccount3.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">6. Edit</h3>
                                <p>Address can be edited.</p>
                                <h3 class="mt-40">7. Add New Address</h3>
                                <p>You can add new address to your account.</p>

                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- My Wallet -->
            <section class="infix-documentation mt-40" id="MyWallet">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">My Wallet</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Wallet</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerdashboard/mywallte1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Recharge Using Gift Card </h3>
                                <p>Recharge your wallet using gift card.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart My Wallet Recharge</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerdashboard/mywallte2.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Redeem Now</h3>
                                <p>Redeem gift card bonus.</p>

                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Referral -->
            <section class="infix-documentation mt-40" id="Referral">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Referral</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Referral</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerdashboard/referral.png"
                                    class="img img-responsive general-setting documentationImg">
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- Support Ticket -->
            <section class="infix-documentation mt-40" id="SupportTicket">
                <div class="container-fluid p-0">
                    <div class="row">
                        <div class="col-lg-4 no-gutters">
                            <div class="main-title">
                                <h3 class="mb-30">Support Ticket</h3>
                            </div>
                        </div>
                    </div>
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Support Ticket</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerdashboard/supportticket1.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Dropdown</h3>
                                <p>Select the type of the ticket to show.</p>
                                <h3 class="mt-40">2. Add New</h3>
                                <p>Add new support ticket.</p>
                                <h3 class="mt-40">3. View</h3>
                                <p>View ticket details.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Support Ticket Create</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerdashboard/supportticket2.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Create </h3>
                                <p>Add new support ticket.</p>

                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Support Ticket Create</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerdashboard/supportticket3.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Reply </h3>
                                <p>To reply click the button.</p>

                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section class="infix-documentation mt-40" id="">
                <div class="container-fluid p-0">
                    <div class="white-box ">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="main-title">
                                    <h3 class="mb-30 text-center">Amazcart Support Ticket Create</h3>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <img src="img/customerdashboard/supportticket4.png"
                                    class="img img-responsive general-setting documentationImg">
                                <h3 class="mt-40">1. Reply </h3>
                                <p>Write the description and reply.</p>

                            </div>
                        </div>
                    </div>
                </div>
            </section>



            <!--            End Parent Panel-->
        </div>
    </div>

    <!-- ================Footer Area ================= -->
    <footer class="footer-area">
        <div class="container">
            <div class="row">
                <div class="col-lg-12"></div>
            </div>
        </div>
    </footer>
    <!-- ================End Footer Area ================= -->

    <!-- Optional JavaScript -->
    <!-- jQuery first, then Popper.js, then Bootstrap JS -->
    <script src="vendors/js/jquery-3.2.1.min.js">
    </script>
    <script src="vendors/js/jquery-ui.js">
    </script>
    <script src="vendors/js/jquery.data-tables.js">
    </script>
    <script src="vendors/js/dataTables.buttons.min.js">
    </script>
    <script src="vendors/js/buttons.flash.min.js">
    </script>
    <script src="vendors/js/jszip.min.js">
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/pdfmake.min.js">
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/vfs_fonts.js">
    </script>
    <script src="vendors/js/buttons.html5.min.js">
    </script>
    <script src="vendors/js/buttons.print.min.js">
    </script>
    <script src="vendors/js/dataTables.rowReorder.min.js">
    </script>
    <script src="vendors/js/dataTables.responsive.min.js">
    </script>
    <script src="vendors/js/buttons.colVis.min.js">
    </script>
    <script src="vendors/js/popper.js">
    </script>
    <script src="vendors/js/bootstrap.min.js">
    </script>
    <script src="vendors/js/nice-select.min.js">
    </script>
    <script src="vendors/js/jquery.magnific-popup.min.js">
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2. 18.1/moment.js" type="text/javascript"></script>
    <script
        src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4. 17.47/js/bootstrap-datetimepicker.min.js"></script>
    <script src="vendors/js/bootstrap-datepicker.min.js">
    </script>
    <script src="js/main.js">
    </script>
    <script type="text/javascript">
        $(document).ready(function () {
            var sections = [];
            var scrolled_id = false;
            var id = false;
            var $navbar = $('#sidebar');
            var $navbar__links = $navbar.find('.spy_scroll');

            $navbar__links.each(function () {
                sections.push($($(this).attr('href')));
            });

            $('.spy_scroll').bind('click', function (event) {
                var $anchor = $(this);
                var headerH = '0';
                $('html, body').stop().animate({
                    scrollTop: $($anchor.attr('href')).offset().top - headerH + "px"
                }, 5000, 'easeInOutExpo');
                event.preventDefault();
            });

            $(window).scroll(function (e) {
                e.preventDefault();
                var scrollTop = $(this).scrollTop() + ($(window).height() / 3);


                for (var i in sections) {
                    var section = sections[i];

                    if (scrollTop > section.offset().top) {
                        scrolled_id = section.attr('id');
                    }

                    if (scrolled_id !== id) {
                        id = scrolled_id;

                        $navbar__links.removeClass('spy_scroll--current');

                        $('a[href="#' + id + '"]', $navbar).addClass('spy_scroll--current');
                        $('a[href="#' + id + '"]', $navbar).closest('.list-unstyled').addClass('show');
                        $('a[href="#' + id + '"]', $navbar).closest('.list-unstyled').siblings('.dropdown-toggle').addClass('active');
                        // $('a[href="#' + id + '"]', $navbar).closest('.list-unstyled').addClass('active'); 


                    }
                }
            });

            $(window).trigger('scroll');
        });

    </script>
</body>

</html>