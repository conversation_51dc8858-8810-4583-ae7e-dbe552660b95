@extends('backEnd.master')
@section('page-title', app('general_setting')->site_title)
@section('mainContent')
<section class="admin-visitor-area up_st_admin_visitor">
    <div class="container-fluid p-0">
        <div class="container-fluid p-0 mb-5">
            <div class="row justify-content-center">
                <div class="col-lg-12">
                    <div class="box_header common_table_header">
                        <div class="main-title d-md-flex">
                            <h3 class="mb-0 mr-30 mb_xs_15px mb_sm_20px">{{ __('report.filter_selection_criteria') }}
                                {{ __('common.for') }} {{ __('common.order') }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-lg-12">
                    <div class="white_box_50px box_shadow_white pb-3">
                        <form class="" action="{{ route('seller_report.order') }}" method="GET">
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="primary_input mb-15">
                                        <label class="primary_input_label" for="">{{ __('common.type') }}</label>
                                        <select required class="primary_select mb-15" name="type" id="type">
                                            <option value="">{{ __('common.select_one') }}</option>

                                            <option @if(!isset($type)) selected @endif @if(isset($type) && $type=="all"
                                                ) selected @endif value="all">{{ __('common.all') }}
                                                {{ __('common.order') }}</option>

                                            <option @if(isset($type) && $type=="pending" ) selected @endif
                                                value="pending">{{__('order.pending_orders')}}</option>
                                            <option @if(isset($type) && $type=="confirmed" ) selected @endif
                                                value="confirmed">{{__('order.confirmed_orders')}}</option>
                                            <option @if(isset($type) && $type=="completed" ) selected @endif
                                                value="completed">{{__('order.completed_orders')}}</option>
                                            <option @if(isset($type) && $type=="inhouse" ) selected @endif
                                                value="inhouse">{{__('order.inhouse_orders')}}</option>
                                        </select>
                                        <span class="text-danger">{{$errors->first('seller_id')}}</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="primary_input mb-15">
                                        <label class="primary_input_label" for="">{{__('common.date')}}</label>
                                        <div class="primary_datepicker_input">
                                            <div class="no-gutters input-right-icon">
                                                <div class="col">
                                                    <div class="">

                                                        <input id="reportrange" placeholder="{{__('common.date')}}"
                                                            class="primary_input_field primary-input form-control"
                                                            type="text" name="reportrange" autocomplete="off" readonly
                                                            required>
                                                        <input type="hidden" name="start_date" id="start_date"
                                                            value="01-01-2020">
                                                        <input type="hidden" name="end_date" id="end_date"
                                                            value="{{ date('d-m-Y',strtotime(today())) }}">
                                                    </div>
                                                </div>
                                                <button class="" type="button">
                                                    <i class="ti-calendar" id="start-date-icon"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row justify-content-center">
                                <div class="primary_input">
                                    <button type="submit" class="primary-btn fix-gr-bg" id="save_button_parent"><i
                                            class="ti-search"></i>{{ __('report.search') }}</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="row justify-content-center">
            <div class="col-lg-12">
                @if (isset($type) && $type == "all")
                <div role="tabpanel" class="tab-pane fade active show" id="order_all_data">
                    <div class="box_header common_table_header ">
                        <div class="main-title d-md-flex">
                            <h3 class="mb-0 mr-30 mb_xs_15px mb_sm_20px">{{ __('common.all') }}
                                {{ __('common.order') }}</h3>
                        </div>
                    </div>
                    <div class="QA_section QA_section_heading_custom check_box_table">
                        <div class="QA_table">

                            <div class="" id="latest_order_div">
                                <table class="table" id="allOrderTable">
                                    <thead>
                                        <tr>
                                            <th>{{__('common.sl')}}</th>
                                            <th width="10%">{{__('common.date')}}</th>
                                            <th>{{__('common.order_id')}}</th>
                                            <th>{{__('common.email')}}</th>
                                            <th>{{__('order.product_qty')}}</th>
                                            <th>{{__('common.total_amount')}}</th>
                                            <th>{{__('order.order_status')}}</th>
                                            <th>{{__('order.is_paid')}}</th>
                                        </tr>
                                    </thead>

                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
                @if (isset($type) && $type == "pending")
                <div role="tabpanel" class="tab-pane fade active show" id="order_pending_data">
                    <div class="box_header common_table_header ">
                        <div class="main-title d-md-flex">
                            <h3 class="mb-0 mr-30 mb_xs_15px mb_sm_20px">{{__('order.pending_orders')}}</h3>
                        </div>
                    </div>
                    <div class="QA_section QA_section_heading_custom check_box_table">
                        <div class="QA_table">

                            <div class="" id="latest_order_div">
                                <table class="table" id="orderPendingTable">
                                    <thead>
                                        <tr>
                                            <th>{{__('common.sl')}}</th>
                                            <th width="10%">{{__('common.date')}}</th>
                                            <th>{{__('common.order_id')}}</th>
                                            <th>{{__('common.email')}}</th>
                                            <th>{{__('order.product_qty')}}</th>
                                            <th>{{__('common.total_amount')}}</th>
                                            <th>{{__('order.order_status')}}</th>
                                            <th>{{__('order.is_paid')}}</th>
                                        </tr>
                                    </thead>

                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
                @if (isset($type) && $type == "confirmed")
                <div role="tabpanel" class="tab-pane fade active show" id="order_confirmed_data">
                    <div class="box_header common_table_header ">
                        <div class="main-title d-md-flex">
                            <h3 class="mb-0 mr-30 mb_xs_15px mb_sm_20px">{{__('order.confirmed_orders')}} </h3>
                        </div>
                    </div>
                    <div class="QA_section QA_section_heading_custom check_box_table">
                        <div class="QA_table">

                            <div class="" id="latest_order_div">
                                <table class="table" id="confirmedTable">
                                    <thead>
                                        <tr>
                                            <th>{{__('common.sl')}}</th>
                                            <th width="10%">{{__('common.date')}}</th>
                                            <th>{{__('common.order_id')}}</th>
                                            <th>{{__('common.email')}}</th>
                                            <th>{{__('order.product_qty')}}</th>
                                            <th>{{__('common.total_amount')}}</th>
                                            <th>{{__('order.order_status')}}</th>
                                            <th>{{__('order.is_paid')}}</th>
                                        </tr>
                                    </thead>

                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
                @if (isset($type) && $type == "completed")
                <div role="tabpanel" class="tab-pane fade active show" id="order_complete_data">
                    <div class="box_header common_table_header ">
                        <div class="main-title d-md-flex">
                            <h3 class="mb-0 mr-30 mb_xs_15px mb_sm_20px">{{__('order.completed_orders')}}</h3>
                        </div>
                    </div>
                    <div class="QA_section QA_section_heading_custom check_box_table">
                        <div class="QA_table">

                            <div class="" id="latest_order_div">
                                <table class="table" id="completedTable">
                                    <thead>
                                        <tr>
                                            <th>{{__('common.sl')}}</th>
                                            <th width="10%">{{__('common.date')}}</th>
                                            <th>{{__('common.order_id')}}</th>
                                            <th>{{__('common.email')}}</th>
                                            <th>{{__('order.product_qty')}}</th>
                                            <th>{{__('common.total_amount')}}</th>
                                            <th>{{__('order.order_status')}}</th>
                                            <th>{{__('order.is_paid')}}</th>
                                        </tr>
                                    </thead>

                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
                @if (isset($type) && $type == "inhouse")
                <div role="tabpanel" class="tab-pane fade active show" id="inhouse_order_data">
                    <div class="box_header common_table_header ">
                        <div class="main-title d-md-flex">
                            <h3 class="mb-0 mr-30 mb_xs_15px mb_sm_20px">{{__('order.inhouse_orders')}}</h3>
                        </div>
                    </div>

                    <div class="QA_section QA_section_heading_custom check_box_table">
                        <div class="QA_table">

                            <div class="" id="latest_order_div">
                                <table class="table" id="inhouseOrderTable">
                                    <thead>
                                        <tr>
                                            <th>{{__('common.sl')}}</th>
                                            <th width="10%">{{__('common.date')}}</th>
                                            <th>{{__('common.order_id')}}</th>
                                            <th>{{__('common.email')}}</th>
                                            <th>{{__('order.product_qty')}}</th>
                                            <th>{{__('common.total_amount')}}</th>
                                            <th>{{__('order.order_status')}}</th>
                                            <th>{{__('order.is_paid')}}</th>
                                        </tr>
                                    </thead>

                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script type="text/javascript">
    $(function() {
    @if (isset($start_date) && isset($end_date))
        var start = moment("{{ date('m/d/Y',strtotime($start_date)) }}");
        var end = moment("{{ date('m/d/Y',strtotime($end_date)) }}");
    @else
        var start = moment();
        var end = moment();
    @endif
    function cb(start, end) {
        $('#reportrange span').html(start.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));
        $('#start_date').val(start.format('DD-MM-YYYY'));
        $('#end_date').val(end.format('DD-MM-YYYY'));
    }
    $('#reportrange').daterangepicker({
        startDate: start,
        endDate: end,
        "buttonClasses": "primary-btn fix-gr-bg",
        "applyButtonClasses": "primary-btn fix-gr-bg",
        "cancelClass": "primary-btn fix-gr-bg",
        "timePicker": false,
        "linkedCalendars": false,
        ranges: {
        'Today': [moment(), moment()],
        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
        'Last 30 Days': [moment().subtract(29, 'days'), moment()],
        'This Month': [moment().startOf('month'), moment().endOf('month')],
        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
        'This Year': [moment().startOf('year'), moment().endOf('year')],
        'Last Year': [moment().subtract(1, 'year').startOf('year'), moment().subtract(1, 'year').endOf('year')],
        }
    }, cb);

    cb(start, end);
});
(function($){
    "use strict";
    $('#allOrderTable').DataTable({
            processing: true,
            serverSide: true,
            stateSave: true,
            "ajax": ( {
                url: "{{ route('seller_report.order_data') }}" + '?table=all&start_date={{ $start_date }}&end_date={{ $end_date }}'
            }),
            "initComplete":function(json){

            },
            columns: [
                { data: 'DT_RowIndex', name: 'id' },
                { data: 'date', name: 'date' },
                { data: 'order_number', name: 'order_number' },
                { data: 'email', name: 'email' },
                { data: 'total_qty', name: 'total_qty' },
                { data: 'total_amount', name: 'total_amount' },
                { data: 'order_status', name: 'order_status' },
                { data: 'is_paid', name: 'is_paid' },

            ],

            bLengthChange: false,
            "bDestroy": true,
            language: {
                search: "<i class='ti-search'></i>",
                searchPlaceholder: trans('common.quick_search'),
                paginate: {
                    next: "<i class='ti-arrow-right'></i>",
                    previous: "<i class='ti-arrow-left'></i>"
                }
            },
            dom: 'Bfrtip',
            buttons: [{
                    extend: 'copyHtml5',
                    text: '<i class="fa fa-files-o"></i>',
                    title: $("#header_title").text(),
                    titleAttr: 'Copy',
                    exportOptions: {
                        columns: ':visible',
                        columns: ':not(:last-child)',
                    }
                },
                {
                    extend: 'excelHtml5',
                    text: '<i class="fa fa-file-excel-o"></i>',
                    titleAttr: 'Excel',
                    title: $("#header_title").text(),
                    margin: [10, 10, 10, 0],
                    exportOptions: {
                        columns: ':visible',
                        columns: ':not(:last-child)',
                    },

                },
                {
                    extend: 'csvHtml5',
                    text: '<i class="fa fa-file-text-o"></i>',
                    titleAttr: 'CSV',
                    exportOptions: {
                        columns: ':visible',
                        columns: ':not(:last-child)',
                    }
                },
                {
                    extend: 'pdfHtml5',
                    text: '<i class="fa fa-file-pdf-o"></i>',
                    title: $("#header_title").text(),
                    titleAttr: 'PDF',
                    exportOptions: {
                        columns: ':visible',
                        columns: ':not(:last-child)',
                    },
                    orientation: 'landscape',
                    pageSize: 'A4',
                    margin: [0, 0, 0, 0],
                    alignment: 'center',
                    header: true,

                },
                {
                    extend: 'print',
                    text: '<i class="fa fa-print"></i>',
                    titleAttr: 'Print',
                    title: $("#header_title").text(),
                    exportOptions: {
                        columns: ':not(:last-child)',
                    }
                },
                {
                    extend: 'colvis',
                    text: '<i class="fa fa-columns"></i>',
                    postfixButtons: ['colvisRestore']
                }
            ],
            columnDefs: [{
                visible: false
            }],
            responsive: true,
    });
    $('#orderPendingTable').DataTable({
            processing: true,
            serverSide: true,
            stateSave: true,
            "ajax": ( {
                url: "{{ route('seller_report.order_data') }}" + '?table=pending&start_date={{ $start_date }}&end_date={{ $end_date }}'
            }),
            "initComplete":function(json){

            },
            columns: [
                { data: 'DT_RowIndex', name: 'id' },
                { data: 'date', name: 'date' },
                { data: 'order_number', name: 'order_number' },
                { data: 'email', name: 'email' },
                { data: 'total_qty', name: 'total_qty' },
                { data: 'total_amount', name: 'total_amount' },
                { data: 'order_status', name: 'order_status' },
                { data: 'is_paid', name: 'is_paid' },

            ],

            bLengthChange: false,
            "bDestroy": true,
            language: {
                search: "<i class='ti-search'></i>",
                searchPlaceholder: trans('common.quick_search'),
                paginate: {
                    next: "<i class='ti-arrow-right'></i>",
                    previous: "<i class='ti-arrow-left'></i>"
                }
            },
            dom: 'Bfrtip',
            buttons: [{
                    extend: 'copyHtml5',
                    text: '<i class="fa fa-files-o"></i>',
                    title: $("#header_title").text(),
                    titleAttr: 'Copy',
                    exportOptions: {
                        columns: ':visible',
                        columns: ':not(:last-child)',
                    }
                },
                {
                    extend: 'excelHtml5',
                    text: '<i class="fa fa-file-excel-o"></i>',
                    titleAttr: 'Excel',
                    title: $("#header_title").text(),
                    margin: [10, 10, 10, 0],
                    exportOptions: {
                        columns: ':visible',
                        columns: ':not(:last-child)',
                    },

                },
                {
                    extend: 'csvHtml5',
                    text: '<i class="fa fa-file-text-o"></i>',
                    titleAttr: 'CSV',
                    exportOptions: {
                        columns: ':visible',
                        columns: ':not(:last-child)',
                    }
                },
                {
                    extend: 'pdfHtml5',
                    text: '<i class="fa fa-file-pdf-o"></i>',
                    title: $("#header_title").text(),
                    titleAttr: 'PDF',
                    exportOptions: {
                        columns: ':visible',
                        columns: ':not(:last-child)',
                    },
                    pageSize: 'A4',
                    margin: [0, 0, 0, 0],
                    alignment: 'center',
                    header: true,

                },
                {
                    extend: 'print',
                    text: '<i class="fa fa-print"></i>',
                    titleAttr: 'Print',
                    title: $("#header_title").text(),
                    exportOptions: {
                        columns: ':not(:last-child)',
                    }
                },
                {
                    extend: 'colvis',
                    text: '<i class="fa fa-columns"></i>',
                    postfixButtons: ['colvisRestore']
                }
            ],
            columnDefs: [{
                visible: false
            }],
            responsive: true,
    });
    $('#confirmedTable').DataTable({
        processing: true,
        serverSide: true,
        stateSave: true,
        "ajax": ( {
            url: "{{ route('seller_report.order_data') }}" + '?table=confirmed&start_date={{ $start_date }}&end_date={{ $end_date }}'
        }),
        "initComplete":function(json){

        },
        columns: [
            { data: 'DT_RowIndex', name: 'id' },
            { data: 'date', name: 'date' },
            { data: 'order_number', name: 'order_number' },
            { data: 'email', name: 'email' },
            { data: 'total_qty', name: 'total_qty' },
            { data: 'total_amount', name: 'total_amount' },
            { data: 'order_status', name: 'order_status' },
            { data: 'is_paid', name: 'is_paid' },

        ],

        bLengthChange: false,
        "bDestroy": true,
        language: {
            search: "<i class='ti-search'></i>",
            searchPlaceholder: trans('common.quick_search'),
            paginate: {
                next: "<i class='ti-arrow-right'></i>",
                previous: "<i class='ti-arrow-left'></i>"
            }
        },
        dom: 'Bfrtip',
        buttons: [{
                extend: 'copyHtml5',
                text: '<i class="fa fa-files-o"></i>',
                title: $("#header_title").text(),
                titleAttr: 'Copy',
                exportOptions: {
                    columns: ':visible',
                    columns: ':not(:last-child)',
                }
            },
            {
                extend: 'excelHtml5',
                text: '<i class="fa fa-file-excel-o"></i>',
                titleAttr: 'Excel',
                title: $("#header_title").text(),
                margin: [10, 10, 10, 0],
                exportOptions: {
                    columns: ':visible',
                    columns: ':not(:last-child)',
                },

            },
            {
                extend: 'csvHtml5',
                text: '<i class="fa fa-file-text-o"></i>',
                titleAttr: 'CSV',
                exportOptions: {
                    columns: ':visible',
                    columns: ':not(:last-child)',
                }
            },
            {
                extend: 'pdfHtml5',
                text: '<i class="fa fa-file-pdf-o"></i>',
                title: $("#header_title").text(),
                titleAttr: 'PDF',
                exportOptions: {
                    columns: ':visible',
                    columns: ':not(:last-child)',
                },
                pageSize: 'A4',
                margin: [0, 0, 0, 0],
                alignment: 'center',
                header: true,

            },
            {
                extend: 'print',
                text: '<i class="fa fa-print"></i>',
                titleAttr: 'Print',
                title: $("#header_title").text(),
                exportOptions: {
                    columns: ':not(:last-child)',
                }
            },
            {
                extend: 'colvis',
                text: '<i class="fa fa-columns"></i>',
                postfixButtons: ['colvisRestore']
            }
        ],
        columnDefs: [{
            visible: false
        }],
        responsive: true,
    });
    $('#completedTable').DataTable({
        processing: true,
        serverSide: true,
        stateSave: true,
        "ajax": ( {
            url: "{{ route('seller_report.order_data') }}" + '?table=completed&start_date={{ $start_date }}&end_date={{ $end_date }}'
        }),
        "initComplete":function(json){

        },
        columns: [
            { data: 'DT_RowIndex', name: 'id' },
            { data: 'date', name: 'date' },
            { data: 'order_number', name: 'order_number' },
            { data: 'email', name: 'email' },
            { data: 'total_qty', name: 'total_qty' },
            { data: 'total_amount', name: 'total_amount' },
            { data: 'order_status', name: 'order_status' },
            { data: 'is_paid', name: 'is_paid' },

        ],

        bLengthChange: false,
        "bDestroy": true,
        language: {
            search: "<i class='ti-search'></i>",
            searchPlaceholder: trans('common.quick_search'),
            paginate: {
                next: "<i class='ti-arrow-right'></i>",
                previous: "<i class='ti-arrow-left'></i>"
            }
        },
        dom: 'Bfrtip',
        buttons: [{
                extend: 'copyHtml5',
                text: '<i class="fa fa-files-o"></i>',
                title: $("#header_title").text(),
                titleAttr: 'Copy',
                exportOptions: {
                    columns: ':visible',
                    columns: ':not(:last-child)',
                }
            },
            {
                extend: 'excelHtml5',
                text: '<i class="fa fa-file-excel-o"></i>',
                titleAttr: 'Excel',
                title: $("#header_title").text(),
                margin: [10, 10, 10, 0],
                exportOptions: {
                    columns: ':visible',
                    columns: ':not(:last-child)',
                },

            },
            {
                extend: 'csvHtml5',
                text: '<i class="fa fa-file-text-o"></i>',
                titleAttr: 'CSV',
                exportOptions: {
                    columns: ':visible',
                    columns: ':not(:last-child)',
                }
            },
            {
                extend: 'pdfHtml5',
                text: '<i class="fa fa-file-pdf-o"></i>',
                title: $("#header_title").text(),
                titleAttr: 'PDF',
                exportOptions: {
                    columns: ':visible',
                    columns: ':not(:last-child)',
                },
                pageSize: 'A4',
                margin: [0, 0, 0, 0],
                alignment: 'center',
                header: true,

            },
            {
                extend: 'print',
                text: '<i class="fa fa-print"></i>',
                titleAttr: 'Print',
                title: $("#header_title").text(),
                exportOptions: {
                    columns: ':not(:last-child)',
                }
            },
            {
                extend: 'colvis',
                text: '<i class="fa fa-columns"></i>',
                postfixButtons: ['colvisRestore']
            }
        ],
        columnDefs: [{
            visible: false
        }],
        responsive: true,
    });
    $('#inhouseOrderTable').DataTable({
        processing: true,
        serverSide: true,
        stateSave: true,
        "ajax": ( {
            url: "{{ route('seller_report.order_data') }}" + '?table=inhouse&start_date={{ $start_date }}&end_date={{ $end_date }}'
        }),
        "initComplete":function(json){

        },
        columns: [
            { data: 'DT_RowIndex', name: 'id' },
            { data: 'date', name: 'date' },
            { data: 'order_number', name: 'order_number' },
            { data: 'email', name: 'email' },
            { data: 'total_qty', name: 'total_qty' },
            { data: 'total_amount', name: 'total_amount' },
            { data: 'order_status', name: 'order_status' },
            { data: 'is_paid', name: 'is_paid' },

        ],

        bLengthChange: false,
        "bDestroy": true,
        language: {
            search: "<i class='ti-search'></i>",
            searchPlaceholder: trans('common.quick_search'),
            paginate: {
                next: "<i class='ti-arrow-right'></i>",
                previous: "<i class='ti-arrow-left'></i>"
            }
        },
        dom: 'Bfrtip',
        buttons: [{
                extend: 'copyHtml5',
                text: '<i class="fa fa-files-o"></i>',
                title: $("#header_title").text(),
                titleAttr: 'Copy',
                exportOptions: {
                    columns: ':visible',
                    columns: ':not(:last-child)',
                }
            },
            {
                extend: 'excelHtml5',
                text: '<i class="fa fa-file-excel-o"></i>',
                titleAttr: 'Excel',
                title: $("#header_title").text(),
                margin: [10, 10, 10, 0],
                exportOptions: {
                    columns: ':visible',
                    columns: ':not(:last-child)',
                },

            },
            {
                extend: 'csvHtml5',
                text: '<i class="fa fa-file-text-o"></i>',
                titleAttr: 'CSV',
                exportOptions: {
                    columns: ':visible',
                    columns: ':not(:last-child)',
                }
            },
            {
                extend: 'pdfHtml5',
                text: '<i class="fa fa-file-pdf-o"></i>',
                title: $("#header_title").text(),
                titleAttr: 'PDF',
                exportOptions: {
                    columns: ':visible',
                    columns: ':not(:last-child)',
                },
                pageSize: 'A4',
                margin: [0, 0, 0, 0],
                alignment: 'center',
                header: true,

            },
            {
                extend: 'print',
                text: '<i class="fa fa-print"></i>',
                titleAttr: 'Print',
                title: $("#header_title").text(),
                exportOptions: {
                    columns: ':not(:last-child)',
                }
            },
            {
                extend: 'colvis',
                text: '<i class="fa fa-columns"></i>',
                postfixButtons: ['colvisRestore']
            }
        ],
        columnDefs: [{
            visible: false
        }],
        responsive: true,
    });
})(jQuery);
</script>
@endpush
