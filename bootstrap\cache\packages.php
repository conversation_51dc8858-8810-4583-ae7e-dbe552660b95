<?php return array (
  'anandsiddharth/laravel-paytm-wallet' => 
  array (
    'providers' => 
    array (
      0 => 'Anand\\LaravelPaytmWallet\\PaytmWalletServiceProvider',
    ),
    'aliases' => 
    array (
      'PaytmWallet' => 'Anand\\LaravelPaytmWallet\\Facades\\PaytmWallet',
    ),
  ),
  'brian2694/laravel-toastr' => 
  array (
    'providers' => 
    array (
      0 => 'Brian2694\\Toastr\\ToastrServiceProvider',
    ),
    'aliases' => 
    array (
      'Toastr' => 'Brian2694\\Toastr\\Facades\\Toastr',
    ),
  ),
  'codezero/laravel-unique-translation' => 
  array (
    'providers' => 
    array (
      0 => 'CodeZero\\UniqueTranslation\\UniqueTranslationServiceProvider',
    ),
  ),
  'freshbitsweb/laravel-google-analytics-4-measurement-protocol' => 
  array (
    'providers' => 
    array (
      0 => 'Freshbitsweb\\LaravelGoogleAnalytics4MeasurementProtocol\\GA4ServiceProvider',
    ),
    'aliases' => 
    array (
      'GA4' => 'Freshbitsweb\\LaravelGoogleAnalytics4MeasurementProtocol\\Facades\\GA4',
    ),
  ),
  'hisorange/browser-detect' => 
  array (
    'providers' => 
    array (
      0 => 'hisorange\\BrowserDetect\\ServiceProvider',
    ),
    'aliases' => 
    array (
      'Browser' => 'hisorange\\BrowserDetect\\Facade',
    ),
  ),
  'intervention/image' => 
  array (
    'providers' => 
    array (
      0 => 'Intervention\\Image\\ImageServiceProvider',
    ),
    'aliases' => 
    array (
      'Image' => 'Intervention\\Image\\Facades\\Image',
    ),
  ),
  'jorenvanhocht/laravel-share' => 
  array (
    'providers' => 
    array (
      0 => 'Jorenvh\\Share\\Providers\\ShareServiceProvider',
    ),
    'aliases' => 
    array (
      'Share' => 'Jorenvh\\Share\\ShareFacade',
    ),
  ),
  'kingflamez/laravelrave' => 
  array (
    'providers' => 
    array (
      0 => 'KingFlamez\\Rave\\RaveServiceProvider',
    ),
    'aliases' => 
    array (
      'Rave' => 'KingFlamez\\Rave\\Facades\\Rave',
    ),
  ),
  'laravel/sanctum' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    ),
  ),
  'laravel/socialite' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Socialite\\SocialiteServiceProvider',
    ),
    'aliases' => 
    array (
      'Socialite' => 'Laravel\\Socialite\\Facades\\Socialite',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'laravel/ui' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Ui\\UiServiceProvider',
    ),
  ),
  'laravelcollective/html' => 
  array (
    'providers' => 
    array (
      0 => 'Collective\\Html\\HtmlServiceProvider',
    ),
    'aliases' => 
    array (
      'Form' => 'Collective\\Html\\FormFacade',
      'Html' => 'Collective\\Html\\HtmlFacade',
    ),
  ),
  'livewire/livewire' => 
  array (
    'providers' => 
    array (
      0 => 'Livewire\\LivewireServiceProvider',
    ),
    'aliases' => 
    array (
      'Livewire' => 'Livewire\\Livewire',
    ),
  ),
  'maatwebsite/excel' => 
  array (
    'providers' => 
    array (
      0 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    ),
    'aliases' => 
    array (
      'Excel' => 'Maatwebsite\\Excel\\Facades\\Excel',
    ),
  ),
  'marcandreappel/laravel-backblaze-b2' => 
  array (
    'providers' => 
    array (
      0 => '\\MarcAndreAppel\\BackblazeB2\\BackblazeB2ServiceProvider',
    ),
  ),
  'mews/purifier' => 
  array (
    'providers' => 
    array (
      0 => 'Mews\\Purifier\\PurifierServiceProvider',
    ),
    'aliases' => 
    array (
      'Purifier' => 'Mews\\Purifier\\Facades\\Purifier',
    ),
  ),
  'milon/barcode' => 
  array (
    'providers' => 
    array (
      0 => 'Milon\\Barcode\\BarcodeServiceProvider',
    ),
    'aliases' => 
    array (
      'DNS1D' => 'Milon\\Barcode\\Facades\\DNS1DFacade',
      'DNS2D' => 'Milon\\Barcode\\Facades\\DNS2DFacade',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'niklasravnsborg/laravel-pdf' => 
  array (
    'providers' => 
    array (
      0 => 'niklasravnsborg\\LaravelPdf\\PdfServiceProvider',
    ),
    'aliases' => 
    array (
      'PDF' => 'niklasravnsborg\\LaravelPdf\\Facades\\Pdf',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'nwidart/laravel-modules' => 
  array (
    'providers' => 
    array (
      0 => 'Nwidart\\Modules\\LaravelModulesServiceProvider',
    ),
    'aliases' => 
    array (
      'Module' => 'Nwidart\\Modules\\Facades\\Module',
    ),
  ),
  'seshac/laravel-shiprocket-api' => 
  array (
    'providers' => 
    array (
      0 => 'Seshac\\Shiprocket\\ShiprocketServiceProvider',
    ),
    'aliases' => 
    array (
      'Shiprocket' => 'Seshac\\Shiprocket\\Shiprocket',
    ),
  ),
  'silviolleite/laravelpwa' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelPWA\\Providers\\LaravelPWAServiceProvider',
    ),
  ),
  'spatie/laravel-analytics' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Analytics\\AnalyticsServiceProvider',
    ),
    'aliases' => 
    array (
      'Analytics' => 'Spatie\\Analytics\\Facades\\Analytics',
    ),
  ),
  'spatie/laravel-google-cloud-storage' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\GoogleCloudStorage\\GoogleCloudStorageServiceProvider',
    ),
    'aliases' => 
    array (
      'GoogleCloudStorage' => 'Spatie\\GoogleCloudStorage\\GoogleCloudStorageFacade',
    ),
  ),
  'spatie/laravel-translatable' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Translatable\\TranslatableServiceProvider',
    ),
  ),
  'spondonit/amazcart-service' => 
  array (
    'providers' => 
    array (
      0 => 'SpondonIt\\AmazCartService\\SpondonItAmazCartServiceProvider',
    ),
  ),
  'spondonit/service' => 
  array (
    'providers' => 
    array (
      0 => 'SpondonIt\\Service\\SpondonItServiceProvider',
    ),
  ),
  'stevebauman/location' => 
  array (
    'providers' => 
    array (
      0 => 'Stevebauman\\Location\\LocationServiceProvider',
    ),
    'aliases' => 
    array (
      'Location' => 'Stevebauman\\Location\\Facades\\Location',
    ),
  ),
  'unicodeveloper/laravel-paystack' => 
  array (
    'providers' => 
    array (
      0 => 'Unicodeveloper\\Paystack\\PaystackServiceProvider',
    ),
    'aliases' => 
    array (
      'Paystack' => 'Unicodeveloper\\Paystack\\Facades\\Paystack',
    ),
  ),
  'van-ons/laraberg' => 
  array (
    'providers' => 'VanOns\\Laraberg\\LarabergServiceProvider',
  ),
  'yajra/laravel-datatables-buttons' => 
  array (
    'providers' => 
    array (
      0 => 'Yajra\\DataTables\\ButtonsServiceProvider',
    ),
  ),
  'yajra/laravel-datatables-editor' => 
  array (
    'providers' => 
    array (
      0 => 'Yajra\\DataTables\\EditorServiceProvider',
    ),
  ),
  'yajra/laravel-datatables-export' => 
  array (
    'providers' => 
    array (
      0 => 'Yajra\\DataTables\\ExportServiceProvider',
    ),
  ),
  'yajra/laravel-datatables-fractal' => 
  array (
    'providers' => 
    array (
      0 => 'Yajra\\DataTables\\FractalServiceProvider',
    ),
  ),
  'yajra/laravel-datatables-html' => 
  array (
    'providers' => 
    array (
      0 => 'Yajra\\DataTables\\HtmlServiceProvider',
    ),
  ),
  'yajra/laravel-datatables-oracle' => 
  array (
    'providers' => 
    array (
      0 => 'Yajra\\DataTables\\DataTablesServiceProvider',
    ),
    'aliases' => 
    array (
      'DataTables' => 'Yajra\\DataTables\\Facades\\DataTables',
    ),
  ),
);