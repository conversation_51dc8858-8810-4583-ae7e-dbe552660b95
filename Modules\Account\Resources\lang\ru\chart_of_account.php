<?php
return [
    'Select your Expense Account' => 'Выберите ваш рассходный счет',
    'Chart Of Accounts' => ' Диаграмма счетов',
    'New Account' => ' Новая учетная запись',
    'Add As A Parent Account' => ' Добавьте как счет родителя',
    'Parent account selection will add your account as a sub account' => ' Выбор счета родителя добавит ваш счет как под счет',
    'Select your Income Account' => ' Выберите ваш счет дохода',
    'Account code need to be unique, Leave blank for auto generate an unique account code' => ' Потребность кода счета быть уникален, пробел разрешения для автомобиля произвести уникальный код счета',
    'Selecting a default Account, will remove previously default account for selected item' => ' Выбирать счет дефолта, извлечет ранее дефолт определяет выбранный деталь',
    'Select Default Account For' => ' Отборный дефолт определяет',
    'You can\'t delete an account which has child element' => ' Вы можете \ „t уничтожить счет который имеет элемент ребенка',
    'The requested chart of account is not found' => ' Спрошенная диаграмма счета не найдена',
    'The requested chart of account deleted successful' => ' Спрошенная диаграмма счета уничтожила успешное',
    'The requested chart of account created successful' => ' Спрошенная диаграмма счета создала успешное',
    'Income Account' => ' Счет дохода',
    'Expense Account' => ' Рассходный счет',
    'Select Account' => ' Отборный счет',
    'Payment Method' => ' Метод оплаты',
    'Select your Payment method' => ' Выберите ваш метод оплаты',
    'Edit Account' => 'Редактировать аккаунт',
    'The requested chart of account updated successful' => 'Запрошенный план счета успешно обновлен',
];
