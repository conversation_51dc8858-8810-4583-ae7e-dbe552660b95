<?php
return [
   'Bank Accounts'=>'Банковские счеты',
   'New Account'=>' Новая учетная запись',
   'Select Default Account For'=>' Отборный дефолт определяет',
   'The requested bank account deleted successful'=>' Спрошенный банковский счет уничтожил успешное',
   'The requested bank account created successful'=>' Спрошенный банковский счет создал успешное',
   'The requested bank account updated successful'=>' Спрошенный банковский счет уточнил успешное',
   'Bank Name'=>' Название банка',
   'Branch Name'=>' Имя ветви',
   'Account Name'=>' Типа счета',
   'Account Number'=>' Номер счета',
   "You can\'t delete an account which has child element"=>' Вы можете \ „t уничтожить счет который имеет элемент ребенка',
   'The requested bank account is not found'=>' Спрошенный банковский счет не найден',
];
