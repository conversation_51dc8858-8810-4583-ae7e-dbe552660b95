<?php
return [
    'Select your Expense Account' => 'Select your Expense Account',
    'Chart Of Accounts' => 'Chart Of Accounts',
    'New Account' => 'New Account',
    'Add As A Parent Account' => 'Add As A Parent Account',
    'Parent account selection will add your account as a sub account' => 'Parent account selection will add your account as a sub account',
    'Select your Income Account' => 'Select your Income Account',
    'Account code need to be unique, Leave blank for auto generate an unique account code' => 'Account code need to be unique, Leave blank for auto generate an unique account code',
    'Selecting a default Account, will remove previously default account for selected item' => 'Selecting a default Account, will remove previously default account for selected item',
    'Select Default Account For' => 'Select Default Account For',
    "You can\'t delete an account which has child element" => "You can\'t delete an account which has child element",
    'The requested chart of account is not found' => 'The requested chart of account is not found',
    'The requested chart of account deleted successful' => 'The requested chart of account deleted successful',
    'The requested chart of account created successful' => 'The requested chart of account created successful',
    'Income Account' => 'Income Account',
    'Expense Account' => 'Expense Account',
    'Select Account' => 'Select Account',
    'Payment Method' => 'Payment Method',
    'Select your Payment method' => 'Select your Payment method',
    'Edit Account' => 'Edit Account',
    'The requested chart of account updated successful' => 'The requested chart of account updated successful',
];
